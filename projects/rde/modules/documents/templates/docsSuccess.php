<?php
  TemplateHelper::includePartial('_tabs.php', 'documents');
?>
  <?php if(count($docs)==0): ?>
    <br/>
    Geen documenten gevonden.
  <?php else: ?>
    <table class="default_table default_table_center" id="invoice_list_table">
      <tr class="dataTableHeadingRow">
        <td>Leverancier</td>
        <td>Omschrijving</td>
        <td style="width: 80px;">Datum</td>
        <td>Opmerking</td>
        <td>Alert</td>
        <td>Note Alert</td>
        <td>Pdf</td>
        <td>Actie</td>
      </tr>
      <?php
        /** @var Files $doc */
        foreach($docs as $doc): ?>
          <tr class="dataTableRow trhover">
            <td><?php echo $doc->company->name ?></td>
            <td><?php echo $doc->title ?></td>
            <td><?php echo $doc->getDocumentdate() ?></td>
            <td>
              <?php if($doc->notes!=""): ?>
                <img src="/gsdfw/images/note.png" class="qtipa" data-caption="Opmerking" title="<?php echo escapeForJS(nl2br($doc->notes)) ?>"/>
              <?php endif; ?>
            </td>
            <td>
              <?php if($doc->uploadAlert==1): ?>
                <i class="material-icons" style="color: #982e1a; font-size: 22px;" title="Melding">error</i>
              <?php endif;  ?>
            </td>
            <td>
              <?php if($doc->notesAlert==1): ?>
                <i class="material-icons" style="color: #982e1a; font-size: 22px;" title="Melding document">error</i>
              <?php endif;  ?>
            </td>
            <td>
              <?php echo BtnHelper::getPrint("?action=docopen&id=".$doc->fileId) ?>
            </td>
            <td>
              <?php echo BtnHelper::getEdit('?action=docedit&id='.$doc->fileId) ?>
            </td>
          </tr>
        <?php endforeach; ?>
    </table>
  <?php endif; ?>