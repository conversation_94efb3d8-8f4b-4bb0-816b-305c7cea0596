<?php if($singlepage):  ?>
  <h3><?php echo $quotation->getQuotationNumberFull() ?> - extra producten wijzigen</h3><br/>
<?php else: ?>
  <?php
//  dumpe($_SESSION['wizard']['quotation']->brandId);
    TemplateHelper::includePartial('_wizardheader.php', 'quotation', ['step' =>$step, 'quotation' =>$quotation, 'hasVerstek' =>$hasVerstek]);
  ?>
<?php endif; ?>
<?php writeErrors($errors); ?>
  <div id="step5" class="wizard">
    <form method="post">

      <?php if(count($categories)==0): ?>

        Er zijn geen extra producten beschikbaar voor uw product.

      <?php else: ?>

        <div class="wizard_inleiding">
          Op deze pagina kunt u extra producten bestellen, zoals losse stenen, voegsel en verf passende bij de door u geslecteerde raamdorpelsteen.
          Let op: het betreft hier extra producten. Wilt u alleen kant en klare raamdorpelelementen, dan hoeft u hier niets in te vullen.
        </div>

        <div class="scroller-x">
          <table id="elementtable">
          <tr class="header-row">
            <td colspan="2">
              Naam
            </td>
            <td style="width: 180px">
              Aantal
            </td>
            <td style="width: 120px;text-align: right">
              Stukprijs
            </td>
            <td style="width: 38px;text-align: right"></td>
            <td style="width: 120px;text-align: right">
              Totaal
            </td>
          </tr>

          <?php foreach($categories as $key=>$category): ?>
            <tr class="elementrow producsextrahead">
              <td colspan="6">
                <a href="#" class="category_show" data-cat-id="<?php echo $category->id ?>">
                  <?php echo $category->content->name ?>
                  <span style="float:right;"><span class="fa fa-eye"></span> bekijk</span>
                </a>
              </td>
            </tr>
            <tr class="elementrow">
              <td colspan="6"></td>
            </tr>
            <?php if($category->id==4): ?>
              <tr class="elementrow hidden category_<?php echo $category->id ?>">
                <td colspan="6" style="color: #ce000c;">Let op: er zit standaard al voegsel bij uw bestelling</td>
              </tr>
            <?php endif; ?>
            <?php foreach($category->projects as $key=>$project):
              /** @var Product $product */
              $product = $project->product;
              ?>
            <tr class="elementrow hidden category_<?php echo $category->id ?>">
              <td>
                <?php if($product->getPhoto(true) && $project->glaced_left==0&& $project->glaced_right==0): ?>
                  <a href="<?php echo $product->getPhoto()?>" class="imagegallery">
                    <img src="<?php echo $product->getPhoto(true) ?>" alt="<?php echo escapeForInput($product->getName($_SESSION['lang'])) ?>" title="<?php echo escapeForInput($product->getName($_SESSION['lang'])) ?>" class="img-responsive" />
                  </a>
                <?php endif; ?>
              </td>
              <td><?php echo $project->name ?></td>
              <td>
                <?php if($product->discountgroup_id==4): //raamdorpelkliks krijgen select box ?>
                  <div class="select" style="width: calc(100% - 30px);display: inline-block">
                    <select name="size[<?php echo $key ?>]" id="prod_<?php echo $key ?>" data-id="<?php echo $key ?>" data-cat-id="<?php echo $category->id ?>" class="sizeselect">
                      <?php
                        $start = 100;
                        $step = 100;
                        if($product->id==676 || $product->id==682) { //ventiklik
                          $start = 500;
                          $step = 500;
                        }
                      ?>
                      <option value="">Selecteer...</option>
                      <?php for($tel=$start;$tel<=100000;$tel+=$step): ?>
                        <option value="<?php echo $tel ?>" <?php if(isset($extra_products[$key]) && $extra_products[$key]->size==$tel) echo 'selected' ?>><?php echo $tel ?></option>
                        <?php
                        if($tel>=5000) $step=1000;
                        if($tel>=50000) $step=10000;
                        ?>
                      <?php endfor; ?>
                    </select>
                  </div>
                <?php else: //andere producten standaard invoer veld. ?>
                  <input autocomplete="off" type="text" name="size[<?php echo $key ?>]" id="prod_<?php echo $key ?>" data-id="<?php echo $key ?>" placeholder="<?php echo __('Aantal') ?>..." class="form-input inputnumber size" maxlength="6" value="<?php if(isset($extra_products[$key])) echo $extra_products[$key]->size ?>" data-cat-id="<?php echo $category->id ?>"  style="width: calc(100% - 30px)" data-max="<?php if($product->order_size_max!="") echo $product->order_size_max; ?>"/>
                <?php endif; ?>
                <div style="display: inline-block;width: 25px;vertical-align: middle;">
                  <a href="#" class="plusbasket elementtable_fa" data-id="<?php echo $key ?>" tabindex="-1" >
                    <i class="fa fa-plus-circle"></i>
                  </a>
                  <a href="#" class="minbasket elementtable_fa" data-id="<?php echo $key ?>" tabindex="-1" >
                    <i class="fa fa-minus-circle"></i>
                  </a>
                </div>
              </td>
              <td style="text-align: right;">
                <?php if($product->getStaffel() != ""): ?>
                  Vanaf € <?php echo StringHelper::getPriceDot($product->getPriceByUser(null,false, 10000000)) ?>
                <?php else: ?>
                € <?php echo $prices[$key]["value"] ?>
                <?php endif; ?>
              </td>
              <td>
                <?php if($product->getStaffel() != ""): ?>
                  <?php ob_start(); ?>
                    <table class="table">
                      <tr>
                        <td class="head">Aantal</td>
                        <?php foreach ($product->getStaffel() as $skey => $sprice): ?>
                          <td><?php echo number_format($skey,0,",",".") ?>+</td>
                        <?php endforeach; ?>
                      </tr>
                      <tr>
                        <td class="head">Stukprijs</td>
                        <?php foreach ($product->getStaffel() as $skey => $sprice): ?>
                          <td style="text-align: right;">€ <?php echo getLocalePrice($sprice) ?></td>
                        <?php endforeach; ?>
                      </tr>
                    </table>
                  <?php $my_var = ob_get_clean(); ?>
                  <a class="qtipa fa fa-info-circle" data-caption="Staffelprijs" data-content="<?php echo escapeForInput($my_var) ?>"></a>
                <?php endif ?>
              </td>
              <td class="row_total" id="total_<?php echo $key ?>" style="text-align: right;" data-total=""></td>
            </tr>
            <?php endforeach; ?>
          <?php endforeach; ?>

          <tr class="header-row ">
            <td colspan="5"  style="text-align: right;">
              Totaal extra producten:
            </td>
            <td style="text-align: right;" id="total">  </td>
          </tr>
        </table>
        </div>

      <?php endif; ?>
      <br/><br/>

      <?php if($singlepage):  ?>
        <button type="submit" name="next" id="next" class="btn" style="float: right;"><?php echo __("Opslaan"); ?> <i class="fa fa-chevron-right"></i></button>
      <?php else: ?>
        <button type="submit" name="prev" id="prev" class="btn" style="float: left;" formnovalidate><i class="fa fa-chevron-left"></i> <?php echo __("Vorige stap"); ?></button>
        <button type="submit" name="next" id="next" class="btn" style="float: right;"><?php echo __("Doorgaan"); ?> <i class="fa fa-chevron-right"></i></button>
      <?php endif; ?>
    </form>

  </div>

  <script type="text/javascript">

    blockEnterSubmit();

    var prices = <?php echo json_encode($prices) ?>;

    $(document).ready( function() {

      new SimpleLightbox(".imagegallery", {
        fileExt: false,
      });

      $(".size").change(function() {
        var id = $(this).attr("data-id");
        if ($(this).val() != "") {
          var size = parseInt($(this).val());
          if(isNaN(size)) {
            size = 0;
          }
          else if($(this).attr("data-max").length>0 && size>parseInt($(this).attr("data-max"))) {
            swal("Maximum","U kunt van dit product een maximum van "+$(this).attr("data-max")+" producten bestellen.");
            size = parseInt($(this).attr("data-max"));
          }
          $(this).val(size);
        }
        if ($(this).val()=="" || $(this).val()=="0") {
          $(this).val("");
          $("#total_"+id).text("");
          $("#total_"+id).attr("data-total","");
          calculateTotals();
          return;
        }

        var size = parseInt($(this).val());
        var piecprice = 0;
        if(prices[id].staffel==1) {
          var prevprice = 0;
          for(var count in prices[id].value) {
            prevprice = prices[id].value[count];
            if(size<=count) {
              break;
            }
          }
          piecprice = prevprice;
        }
        else {
          piecprice = prices[id].value;
        }

        var price = size*piecprice;
        if(price!=0 && price!="") {
          $("#total_"+id).text("€ "+decimalNL(price));
          $("#total_"+id).attr("data-total",price);
        }
        else {
          $("#total_"+id).text("");
          $("#total_"+id).attr("data-total","");
        }
        calculateTotals();
      });




      $(".sizeselect").change(function() {
        var id = $(this).attr("data-id");
        if ($(this).val()=="" || $(this).val()=="0") {
          $("#total_"+id).text("");
          $("#total_"+id).attr("data-total","");
          calculateTotals();
          return;
        }

        var size = parseInt($(this).val());
        var piecprice = 0;
        if(prices[id].staffel==1) {
          piecprice = 1000000; //erg duur wanneer onjuist ingevuld
          for(var count in prices[id].value) {
            if(size >= count) {
              piecprice = prices[id].value[count];
            }
          }
        }
        else {
          piecprice = prices[id].value;
        }

        var price = size*piecprice;
        if(price!=0 && price!="") {
          $("#total_"+id).text("€ "+decimalNL(price));
          $("#total_"+id).attr("data-total",price);
        }
        else {
          $("#total_"+id).text("");
          $("#total_"+id).attr("data-total","");
        }
        calculateTotals();
      });


      $(".plusbasket").click(function (event) {
        event.preventDefault();
        var size = $("#prod_"+$(this).attr("data-id"));
        if(size.hasClass("sizeselect")) {
          size.find('option:selected').next().attr('selected', 'selected');
        }
        else {
          if (size.val() != "") {
            size.val(parseInt(size.val()) + 1);
          }
          else {
            size.val(1);
          }
        }
        size.change();
      });

      $(".minbasket").click(function (event) {
        event.preventDefault();
        var size = $("#prod_"+$(this).attr("data-id"));
        if(size.hasClass("sizeselect")) {
          size.find('option:selected').prev().attr('selected', 'selected');
        }
        else {
          if (size.val() != "") {
            var newval = parseInt(size.val()) - 1;
            if (newval <= 0) {
              size.val("");
            }
            else {
              size.val(newval);
            }
          }
          else {
            size.val("");
          }
        }
        size.change();
      });

      $(".category_show").click(function(e) {
        e.preventDefault();
        $(".category_"+$(this).attr("data-cat-id")).toggle();
      });

      $(".size,.sizeselect").each(function() {
        if($(this).val()!="") {
          $(".category_"+$(this).attr("data-cat-id")).show();
        }
      });

      $(".size").change();
      $(".sizeselect").change();
      $(".category_32").show(); //show desinfectie

    });

    function calculateTotals() {
      var total = 0;
      $(".row_total").each(function() {
        var row_total = parseFloat($(this).attr("data-total"));
        if(!isNaN(row_total)) {
          total += row_total;
        }
      });
      if(total>0) {
        $("#total").text("€ "+decimalNL(total));
      }
      else {
        $("#total").text("€ 0.00");
      }
    }

  </script>

