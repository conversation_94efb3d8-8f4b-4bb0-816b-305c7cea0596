  <section>

  <div class="contenttxt">
    <h1>Vooruitbetalen</h1>
    Op deze pagina kunt u uw onbetaalde bestellingen afrekenen.<br/>
    U ziet een overzicht van de bestellingen met status opdracht of akkoord.<br/>
    Wij gaan uw bestellingen in productie nemen wanneer deze zijn betaald.<br/>
    <br/>
    <?php if(count($quotationsGrouped)==0): ?>
      Al uw bestellingen zijn reeds afgerekend.
    <?php else: ?>
      <table class="download" id="offertetable">
        <tbody>
        <tr  style="border-bottom: 1px solid white;">
          <th>Offertenummer</th>
          <th>Offertedatum</th>
          <th>Project / kenmerk</th>
          <th>PDF</th>
          <th>Afgerekend</th>
          <th style="text-align: right;">Bedrag</th>
        </tr>
        <?php foreach($quotationsGrouped as $quotationsGroup): ?>
            <tr class="trhover">
              <th colspan="6">
                <?php if($quotationsGroup["pickup"]): ?>
                  Ophalen bij de fabriek in Bladel
                <?php else: ?>
                  Afleveradres: <?php echo $quotationsGroup["address"]->getAddressFormatted(", ") ?>
                <?php endif; ?>
                <span style="float:right;"><?php echo getLocalePrice($quotationsGroup["meters"]) ?> meter</span>
              </th>
            </tr>
            <?php foreach($quotationsGroup["quotations"] as $quotation): ?>
              <tr class="trhover">
                <td><?php echo $quotation->getQuotationNumberFull() ?></td>
                <td><?php echo $quotation->getQuotationDate() ?></td>
                <td><?php echo $quotation->getName() ?></td>
                <td><a target="_blank" href="?action=pdf&id=<?php echo $quotation->quotationId ?>" title="Bekijk de PDF van uw offerte/bestelling"><i class="fa fa-file-pdf-o"></i> Bekijk PDF</a></td>
                <td>
                  <?php if($quotation->payedFlag==1): ?>
                    Ja
                  <?php else: ?>
                    Nee
                  <?php endif; ?>
                </td>
                <td style="text-align: right;">
                  <?php if($quotation->payedFlag==0) echo getLocalePrice($quotation->projectValue) ?>
                </td>
              </tr>
            <?php endforeach; ?>
            <?php if($quotationsGroup["freightcosts_payed"]>0): ?>
              <tr>
                <td colspan="5" style="text-align: right;">Vrachtkosten reeds betaald</td>
                <td style="text-align: right;">
                  <?php echo getLocalePrice($quotationsGroup["freightcosts_payed"]) ?>
                </td>
              </tr>
            <?php endif; ?>
            <tr>
              <td colspan="5" style="text-align: right;">Vrachtkosten</td>
              <td style="text-align: right;">
                <?php echo getLocalePrice($quotationsGroup["freightcosts"]) ?>
              </td>
            </tr>
            <tr>
              <td colspan="5" style="text-align: right;">Subtotaal excl BTW</td>
              <td style="text-align: right;">
                <?php echo getLocalePrice($quotationsGroup["total"] ) ?>
              </td>
            </tr>
          <?php endforeach; ?>
          <tr>
            <td><br/></td>
          </tr>
          <tr>
            <td colspan="5" style="text-align: right;">Totaal excl BTW</td>
            <td style="text-align: right;">
              <?php echo getLocalePrice($totals["excl"]) ?>
            </td>
          </tr>
          <?php if($totals["vat"]>0): ?>
            <tr>
              <td colspan="5" style="text-align: right;">BTW 21%</td>
              <td style="text-align: right;">
                <?php echo getLocalePrice($totals["vat"]) ?>
              </td>
            </tr>
          <?php endif; ?>

          <tr>
            <td colspan="5" style="text-align: right;">Totaal te betalen</td>
            <td style="text-align: right;font-weight: bold;">
              <?php echo getLocalePrice($totals["total"]) ?>
            </td>
          </tr>

          <tr>
            <td colspan="4" style="text-align: right;">Online betalen via iDeal</td>
            <td colspan="2" style="text-align: right;font-weight: bold;">
              <form method="post"><button type="submit" class="btn btn-primary" name="go">BETALEN <i class="fa fa-chevron-right"></i></button></form>
            </td>
          </tr>

        </tbody>
      </table>

    <?php endif; ?>

  </div>
</section>

  <script type="text/javascript">
    $(document).ready( function() {
    });
  </script>
