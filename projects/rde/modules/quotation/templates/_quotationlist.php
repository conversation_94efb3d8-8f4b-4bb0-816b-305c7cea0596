<?php if(count($quotations)==0): ?>
  <br/>Er zijn geen offertes gevonden.<br/><br/>
<?php else: ?>

  <div class="x-scroller">
    <table class="download" id="offertetable">
      <tbody>
      <tr>
        <th style="min-width: 26px;">
          <a href="javascript:void(0);" tabindex="-1" class="qtipa fa fa_help fa-info-circle" data-content="<?php echo escapeForInput(TemplateHelper::getPartial("_legenda.php","quotation")) ?>"></a>
          <div id="legenda">

          </div>
        </th>
        <th style="min-width: 115px;">Offertenr.</th>
        <th style="min-width: 92px;">Offertedatum</th>
        <th style="min-width: 90px;">Project / kenmerk</th>
        <th style="min-width: 80px;">Bestel</th>
        <th style="min-width: 90px;">Bewerk</th>
        <th style="min-width: 60px;">Info</th>
      </tr>
      <?php
        $cargoreceipts = [];
        /** @var Quotations $quotation */
        foreach($quotations as $quotation): ?>
          <tr class="trhover">
            <td>
              <?php echo $quotation->getIconHtml() ?>
            </td>
            <td><?php echo $quotation->getQuotationNumberFull() ?></td>
            <td><?php echo $quotation->getQuotationDate() ?></td>
            <td><?php echo $quotation->getName() ?></td>
            <td>
              <?php if($quotation->onHoldFlag==1): ?>
                <span class="qtipa" title="Deze bestelling is geblokkeerd op uw verzoek.">Wachten</span>
              <?php elseif($quotation->mayConfirm()): ?>
                <a href="?action=confirm&id=<?php echo $quotation->quotationId ?><?php if(!isset($showhistorylink)) echo '&history=true'; ?>" title="Bestellen" >Bestel <i class="fa fa-chevron-right"></i></a>
              <?php elseif($quotation->maySplit()): ?>
                <a href="?action=split1&splitreset=true&id=<?php echo $quotation->quotationId ?><?php if(!isset($showhistorylink)) echo '&history=true'; ?>" title="Splits de bestelling in meerdere deelleveringen">Splitsen <i class="fa fa-chevron-right"></i></a>
              <?php endif; ?>
            </td>
            <td>
              <?php if($quotation->mayEdit()): ?>
                <a href="?action=edit&id=<?php echo $quotation->quotationId ?>" title="Bewerk uw offerte" ><i class="fa fa-edit"></i> Bewerk</a>
              <?php endif; ?>
            </td>
            <td>
              <a href="#" title="Toon extra informatie" class="show_info" data-id="<?php echo $quotation->quotationId ?>"><i class="fa fa-info-circle"></i> Info</a>
            </td>
          </tr>
          <tr class="quotation_info_row" id="row_<?php echo $quotation->quotationId ?>">
            <td colspan="7">
              <?php
                $showleverdatum = isset($quotation->route);
                if($showleverdatum) {
                  $adddays = 3;
                  if(date("N",time())>=3) {
                    $adddays +=2; //weekend overslaan
                  }
                  if(strtotime("+".$adddays." DAYS")<intval($quotation->route->getDate("U"))) {
                    $showleverdatum = false;
                  }
                }
              ?>
              <table>
                <tr>
                  <td> Aangemaakt door: </td>
                  <td>
                    <?php if(isset($users[$quotation->userId]) && $users[$quotation->userId]): ?>
                      <?php echo $users[$quotation->userId]->getNaam() ?>
                    <?php else: ?>
                      onbekend
                    <?php endif; ?>
                  </td>
                  <td>
                    <?php if($showleverdatum): ?>
                      Leverdatum:
                    <?php elseif($quotation->statusId>=20): ?>
                      Leverweek:
                    <?php endif; ?>
                  </td>
                  <td>
                    <?php if($showleverdatum): ?>
                      <?php echo $quotation->route->getDate() ?>
                    <?php elseif($quotation->statusId>=20): ?>
                      <?php if(isset($quotation->route)): ?>
                        <?php echo $quotation->route->getDate("W") ?>
                      <?php else: ?>
                        <?php echo $quotation->dueDateWeek ?>
                      <?php endif; ?>
                    <?php endif; ?>
                  </td>
                </tr>

                <?php
                  $cr = false;
                  if(isset($quotation->route) && $quotation->route->cargoReceiptId!="" && $quotation->route->cargoReceiptId!="0") {
                    if(!isset($cargoreceipts[$quotation->route->cargoReceiptId])) {
                      $cargoreceipts[$quotation->route->cargoReceiptId] = CargoReceipt::find_by(["cargoReceiptId" => $quotation->route->cargoReceiptId]);
                    }
                    if(isset($cargoreceipts[$quotation->route->cargoReceiptId])) {
                      $cr = $cargoreceipts[$quotation->route->cargoReceiptId];
                    }
                  }
                ?>
                <tr>
                  <td>Meters: </td>
                  <td>
                    <?php if($quotation->meters==0): ?>
                      n.v.t.
                    <?php else: ?>
                      <?php echo $quotation->meters ?> m
                    <?php endif; ?>
                  </td>
                  <td>
                    <?php if($cr): ?>
                      Vrachtbon:
                    <?php endif; ?>
                  </td>
                  <td>
                    <?php if($cr): ?>
                      <a target="_blank" href="?action=pdfcargo&id=<?php echo $quotation->quotationId ?>" title="Bekijk uw vrachtbon" ><i class="fa fa-file-pdf-o"></i></a>
                    <?php endif; ?>
                  </td>
                </tr>

                <tr>
                  <td> Offertedatum: </td>
                  <td>
                    <?php echo $quotation->getQuotationDate() ?>
                  </td>
                  <td>
                    <?php if($cr && $cr->signatureConsigneeName!=""): ?>
                      Vrachtbon getekend:
                    <?php endif; ?>
                  </td>
                  <td>
                    <?php if($cr && $cr->signatureConsigneeName!=""): ?>
                      <?php echo $cr->signatureConsigneeName; ?>
                    <?php endif; ?>
                  </td>
                </tr>

                <?php if($quotation->getProductionDate()!=""): ?>
                  <tr>
                    <td> Besteldatum: </td>
                    <td>
                      <?php echo $quotation->getProductionDate() ?>
                    </td>
                    <td>
                      <?php if($cr && $cr->cargoReceiptType=='standard'): ?>
                        Productiestaat:
                      <?php endif; ?>
                    </td>
                    <td>
                      <?php if($cr && ($cr->cargoReceiptType=='standard' || $cr->cargoReceiptType=='factorypickup')): ?>
                        <a target="_blank" href="?action=pdfproductionstate&id=<?php echo $quotation->quotationId ?>" title="Bekijk uw productiestaat" ><i class="fa fa-file-pdf-o"></i></a>
                      <?php endif; ?>
                    </td>
                  </tr>
                <?php endif; ?>

                <?php if($quotation->createdVia!=Quotations::CREATED_VIA_WIZARD): ?>
                  <tr>
                    <td> Besteld via: </td>
                    <td>
                      <?php echo $quotation->createdVia ?>
                    </td>
                    <td>
                    </td>
                    <td>
                    </td>
                  </tr>
                <?php endif; ?>

              </table>

              <a target="_blank" href="?action=pdf&id=<?php echo $quotation->quotationId ?>" title="Bekijk de PDF van uw offerte/bestelling" class="pdf_rb"><i class="fa fa-file-pdf-o"></i> Bekijk PDF</a>

              <?php if($quotation->mayDelete()): ?>
                <a href="?action=delete&id=<?php echo $quotation->quotationId ?>" class="gsd-delete delete_rb" data-gsd-text="<?php echo __("Weet u zeker dat u deze offerte wilt verwijderen?") ?>" title="Verwijderen"><i class="fa fa-remove"></i> Verwijder offerte</a>
              <?php endif; ?>
              <a href="#" title="Verberg extra informatie" class="show_info show_info_rb" data-id="<?php echo $quotation->quotationId ?>"><i class="fa fa-chevron-up"></i></a>
            </td>
          </tr>

        <?php endforeach; ?>
      </tbody>
    </table>
  </div>
  <?php if(isset($pager)) { echo '<br/>'; $pager->writePreviousNextResp();} ?>

  <?php if(isset($showhistorylink) && $showhistorylink): ?>
    <br/>
    <a href="<?php echo PageMap::getUrl(211) ?>">Alle offertes <i class="icon-arrow-right"></i> </a>
  <?php endif; ?>

<?php endif; ?>

<script type="text/javascript">
  $(document).ready( function() {


    $(".show_info").click(function(e) {
      e.preventDefault();
      var id = $(this).attr("data-id");
      $("#row_"+id).toggle();
    });


  });

</script>
