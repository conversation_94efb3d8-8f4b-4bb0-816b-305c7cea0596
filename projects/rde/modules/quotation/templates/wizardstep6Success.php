<?php TemplateHelper::includePartial('_wizardheader.php', 'quotation', ['step' =>$step, 'quotation' =>$quotation, 'hasVerstek' =>$hasVerstek]); ?>

<div class="wizard_inleiding">
  Hieronder een korte overzicht van uw offerte.
</div>

<?php writeErrors($errors); ?>

<div id="step5" class="wizard">
  <form method="post">

    <div class="form-row">
      <label class="col3 col-form-label"><?php echo __("Project") ?></label>
      <div class="col9 col-form-label">
        <?php echo $quotation->getName() ?>
      </div>
    </div>

    <div class="form-row">
      <label class="col3 col-form-label"><?php echo __("Steen") ?></label>
      <div class="col9 col-form-label">
        <?php if($stone->isBalkje()): ?>
          <?php echo $brand->name; ?>
        <?php else: ?>
          <?php echo $stone->name ?><br/>
          <?php if($quotation->endstone=="true_grooves"): ?>
            Eindstenen met groeven
          <?php elseif($quotation->endstone=="true"): ?>
            Eindstenen met opstaande zijkanten
          <?php elseif($quotation->endstone=="false"): ?>
            Eindstenen met geglazuurde zijkanten
          <?php endif; ?>
        <?php endif; ?>
      </div>
    </div>

    <div class="form-row">
      <label class="col3 col-form-label"><?php echo __("Elementen") ?></label>
      <div class="col9 col12-xs" style="line-height: 2.5;">
        Aantal: <?php
          $size = 0;
          foreach($elements as $el):
            $size += intval($el->amount);
          endforeach;
          echo $size;
        ?><br/>
        Totale lengte: <?php echo $meters ?> meter
      </div>
    </div>

    <?php if(isset($extra_products) && count($extra_products)>0): ?>
      <div class="form-row">
        <label class="col3 col-form-label"><?php echo __("Extra producten") ?></label>
        <div class="col9 col-form-label" style="line-height: 23px;">
          <?php foreach($extra_products as $ep):
            if($ep->size<=0) continue;
            ?>
            <?php echo $ep->size ?>x <?php echo $ep->name ?>
            <br/>
          <?php endforeach; ?>
        </div>
      </div>
    <?php endif; ?>

    <div class="form-row">
      <label class="col3 col-form-label"><?php echo $quotation_extra->isPickup()?__("Ophaaladres"):__("Afleveradres") ?></label>
      <div class="col9 col-form-label">
        <?php if($quotation_extra->isPickup()): ?>
          Afhalen in Bladel
        <?php else: ?>
          <span style="line-height: 23px;display: inline-block;"><?php echo $quotation->getAddressFull() ?></span>
        <?php endif; ?>
      </div>
    </div>

    <div class="form-row">
      <label class="col3 col-form-label"><?php echo __("Algemene voorwaarden") ?></label>
      <div class="col9 col-form-label">
        <label>
          Door verder te gaan ga je akkoord met onze <a href="https://www.raamdorpel.nl/algemene-voorwaarden" target="_blank">Algemene Voorwaarden</a>
        </label>
      </div>
    </div>

    <div class="form-row">
      <label class="col3 col-form-label"><?php echo __("Opmerkingen") ?></label>
      <div class="col9">
        <textarea class="form-input" name="customerNotes" id="customerNotes" oninput="ShowMessage()" placeholder="<?php echo __("Uw opmerkingen of vragen") ?>"><?php echo $quotation->customerNotes ?></textarea>
        <p id="message">Let op: uw opmerking wordt pas gelezen bij opdracht.
          heeft u een algemene vraag dan graag een email sturen.</p>
      </div>
    </div>


    <br/><br/>

    <button type="submit" name="prev" id="prev" class="btn" style="float: left;" formnovalidate><i class="fa fa-chevron-left"></i> <?php echo __("Vorige stap"); ?></button>
    <button type="submit" name="next" id="next" class="btn" style="float: right;"><?php echo __("Offerte aanvragen"); ?> <i class="fa fa-chevron-right"></i></button>
    <?php if(SandboxUsers::isAdmin()): ?>
      <button type="submit" name="next_order" id="next" class="btn" style="float: right;margin-right: 15px;"><?php echo __("Direct bestellen"); ?> <i class="fa fa-chevron-right"></i></button>
    <?php endif; ?>

  </form>

</div>

<script type="text/javascript">

  blockEnterSubmit();

  message = document.getElementById('message');
  customerNotes = document.getElementById('customerNotes');

  function ShowMessage() {
    message.style.display = 'block';
  }
</script>

<style>
  #message {
    display: none;
    color: red;
  }
</style>
