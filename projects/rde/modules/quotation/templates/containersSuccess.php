  <section>

  <div class="contenttxt">
    <h1>Bakken &amp; Rekken</h1>
    Op deze pagina vindt u een overzicht van de bakken &amp; rekken welke nog niet retour zijn.
    Met de knop 'Bak/Rek ophalen' kunt u ons laten weten dat we deze kunnen ophalen.
    <br/>
    <br/>
    <?php if(count($containers)==0): ?>
      Er zijn geen rekken of bakken gevonden.
    <?php else: ?>
      <table class="download" id="offertetable">
        <tbody>
        <tr>
          <th></th>
          <th>Bak/Rek</th>
          <th>Leverdatum</th>
          <th>Afgemeld</th>
          <th style="min-width: 60px;">Info</th>
        </tr>
        <?php foreach($containers as $container): ?>
          <tr class="trhover">
            <td>
              <?php if(strtotime(getTSFromStr($container->deliverdate)." +45 DAYS")<time()): ?>
                <a href="javascript:void(0);" tabindex="-1" title="Bakken/rekken <?php echo floor((time()-strtotime(getTSFromStr($container->deliverdate)))/86400) ?> dagen op locatie." class="fa fa-exclamation-circle qtipa"></a>
              <?php endif; ?>
            </td>
            <td><?php echo $container->containerNumber ?></td>
            <td><?php echo $container->deliverdate ?></td>
            <td>
              <?php
              $nextroute = false;
              foreach($container->container_quotations as $cq):
                if($cq->nextroute=="true"):
                  $nextroute = true;
                  break;
                endif;
              endforeach;
              $address = 'Onbekend. Graag per email doorgeven.';
              if(!$nextroute):
                foreach($cq->quotations as $quot):
                  $address = $quot->getAddressFull(', ');
                  break;
                endforeach;
              endif;
              ?>
              <?php if($nextroute): ?>
                Ja, wordt opgehaald
              <?php else: ?>
                <a href="?pickup=true&id=<?php echo $container->containerId ?><?php if(isset($_GET["hash"])) echo "&hash=".$_GET["hash"]; ?>" class="pickup" data-nr="<?php echo $container->containerNumber ?>" data-address="<?php echo $address ?>">Bak/Rek ophalen <i class="fa fa-chevron-right"></i></a>
              <?php endif; ?>
            </td>
            <td>
              <a href="#" title="Toon extra informatie" class="show_info" data-id="<?php echo $container->containerId ?>"><i class="fa fa-info-circle"></i> Info</a>
            </td>
          </tr>

          <tr class="quotation_info_row" id="row_<?php echo $container->containerId ?>">
            <td colspan="7">
              <table>
                <tr>
                  <td style="width: 180px;">Offertenummers: </td>
                  <td>
                    <?php
                      $ofnrs = [];
                      foreach($container->container_quotations as $cq): ?>
                        <?php foreach($cq->quotations as $quot): ?>
                          <?php $ofnrs[$quot->projectReference] = $quot->getQuotationNumberFull(); ?>
                        <?php endforeach; ?>
                      <?php endforeach; ?>
                    <?php echo implode(", ",$ofnrs) ?>
                  </td>
                </tr>
                <tr>
                  <td>Project namen: </td>
                  <td>
                    <?php
                      $prnames = [];
                      foreach($container->container_quotations as $cq): ?>
                        <?php foreach($cq->quotations as $quot): ?>
                          <?php if($quot->projectName!='') $prnames[$quot->projectName] = $quot->projectName; ?>
                        <?php endforeach; ?>
                      <?php endforeach; ?>
                    <?php echo implode(", ",$prnames) ?>
                  </td>
                </tr>
                <?php
                $kenmerken = [];
                foreach($container->container_quotations as $cq): ?>
                  <?php foreach($cq->quotations as $quot): ?>
                    <?php if($quot->projectReference!='') $kenmerken[$quot->projectReference] = $quot->projectReference; ?>
                  <?php endforeach; ?>
                <?php endforeach; ?>
                <?php if(count($kenmerken)>0): ?>
                  <tr>
                    <td>Project referenties: </td>
                    <td>
                      <?php echo implode(", ",$kenmerken) ?>
                    </td>
                  </tr>
                <?php endif; ?>
                <tr>
                  <td> Afleveradres: </td>
                  <td>
                    <?php foreach($container->container_quotations as $cq): ?>
                      <?php foreach($cq->quotations as $quot): ?>
                        <?php echo $quot->getAddressFull(' '); break;?>
                      <?php endforeach; break;?>
                    <?php endforeach; ?>
                  </td>
                </tr>
                <?php if($container->deliverdatetime!=""): ?>
                  <tr>
                    <td>Aantal dagen op locatie: </td>
                    <td>
                      <?php echo floor((time()-$container->deliverdatetime)/86400) ?> dagen
                    </td>
                  </tr>
                <?php endif; ?>
              </table>

              <a href="#" title="Verberg extra informatie" class="show_info show_info_rb" data-id="<?php echo $container->containerId ?>"><i class="fa fa-chevron-up"></i></a>
            </td>
          </tr>
        <?php endforeach; ?>
        </tbody>
      </table>

    <?php endif; ?>

  </div>
</section>

  <script type="text/javascript">
    $(document).ready( function() {

      $(".show_info").click(function(e) {
        e.preventDefault();
        var id = $(this).attr("data-id");
        $("#row_"+id).toggle();
      });

      $(".pickup").click (function(e) {
        e.preventDefault();
        var url = $(this).attr("href");
        var html = "Graag deze container ophalen op adres:<br/>" +
          $(this).attr("data-address") + "<br/>" +
          "<span style='font-size: 13px;'>* klopt het adres niet, geef dan het juiste adres door per <NAME_EMAIL>.</span>";
        swal({
          title: "Container "+$(this).attr("data-nr")+" ophalen?",
          html: html,
          type: 'question',
          showCancelButton: true,
          confirmButtonColor: '#3085d6',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Ophalen',
          cancelButtonText: 'Annuleren'
        }).then(function(result){
          if (result.value) {
            location.href=url;
          }
        }).catch(swal.noop);
      });


    });

  </script>
