<?php writeErrors($form->getErrors()); ?>

<div id="split">

  <div id="step5" class="wizard contenttxt">

    <h1>Bestelling splitsen: <?php echo $quotation->getQuotationNumberFull() ?></h1>
    Overzicht van uw deelleveringen:<br/><br/>

    <?php for($tel=0;$tel<$parts;$tel++): ?>
      <div class="split_quotaion_block">
        <div style="padding-bottom: 5px;font-weight: bold;">
          <?php echo $quotation->quotationNumber . '-' . $quotation->quotationVersion." - ".$lettersToSplit[$tel] ?>
        </div>
        <table>
          <tr>
            <td>Producten</td>
            <td>
              <table>
                <tr class="header-row">
                  <td>Referentie</td>
                  <td>Lengte</td>
                  <td style="text-align: right;">Aantal</td>
                </tr>
                <?php foreach($elements as $element):
                  if(!isset($quotation_element_amounts[$tel][$element->elementId])) continue;
                  ?>
                  <tr>
                    <td><?php echo $element->referenceName ?></td>
                    <td><?php echo $element->elementLength ?></td>
                    <td style="text-align: right;"><?php echo $quotation_element_amounts[$tel][$element->elementId] ?></td>
                  </tr>
                <?php endforeach; ?>
              </table>
            </td>
          </tr>
          <tr>
            <td>Leverweek</td>
            <td><?php echo substr($form->getElement("week_".$tel)->getValue(),4) ?></td>
          </tr>
          <tr>
            <td>Opmerkingen</td>
            <td><?php echo nl2br($form->getElement("remark_".$tel)->getValue()) ?></td>
          </tr>
        </table>

      </div>
    <?php endfor; ?>

    <form method="post">
      <button type="submit" name="prev" id="prev" class="btn" style="float: left;"><i class="fa fa-chevron-left"></i> <?php echo __("Vorige stap"); ?></button>
      <button type="submit" name="next" id="next" class="btn" style="float: right;"><?php echo __("Bevestigen"); ?> <i class="fa fa-chevron-right"></i></button>
    </form>
  </div>

</div>


<script type="text/javascript">
  $(document).ready(function() {

  });
</script>

<style>
  .split_quotaion_block {
    background: #ebebeb;
    padding: 15px 15px 5px 15px;
    margin-bottom: 15px;
  }
  .split_quotaion_block td {
    padding-left: 0 !important;
  }
</style>