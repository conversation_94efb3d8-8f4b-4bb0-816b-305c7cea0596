<section>

  <div class="contenttxt" id="confirm">
    <h1>Offerte bestellen - <?php echo $quotation->getQuotationNumberFull() ?></h1>
    <?php if($quotation->statusId == Status::STATUS_NEW): ?>
      <p>Op deze pagina kunt u uw offerte omzetten in een bestelling.<br/>

      <div id="step5" class="wizard">
        <form method="post">

          <div class="form-row">
            <label class="col4 col-form-label"><?php echo __("Bekijk offerte") ?></label>
            <div class="col7 col-form-label">
              <a href="?action=pdf&id=<?php echo $quotation->quotationId ?>" target="_blank" class="btn btn-primary"><i class="icon-calculator"></i> BEKIJK OFFERTE <?php echo $quotation->getQuotationNumberFull() ?></a>
            </div>
          </div>
          <br/><br/>
          Door op de knop 'Bestellen' te klikken, word u het winkelmandje gevuld met uw producten en kunt u de offerte omzetten in een bestelling.<br/><br/>


          <a href="<?php echo $returnurl ?>" style="padding: 10px 0;display: inline-block"><i class="icon-arrow-left"></i> Terug naar start pagina</a>
          <button type="submit" name="order" id="order" class="btn" style="float: right;"> <?php echo __("Winkelmandje vullen en bestellen"); ?> <i class="icon-arrow-right"></i></button>

        </form>
      </div>


    <?php else: ?>
      Offerte <?php echo $quotation->getQuotationNumberFull() ?> is al in bestelling of geleverd.
    <?php endif; ?>

    </p>

  </div>

</section>

<script type="text/javascript">

  $(document).ready( function() {
  });

</script>