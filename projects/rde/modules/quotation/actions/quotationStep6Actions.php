<?php

  use domain\elements\service\SplitCalculator;
  use domain\prices\service\SetSmallOrderAddition;

  trait quotationStep6Actions {


    public function executeWizardstep6() {

      $save = true; //opslaan in database

      if (!isset($_SESSION["wizard"]['quotation']) || !isset($_SESSION["wizard"]['elements'])) {
        ResponseHelper::redirect($this->wizardurl);
      }
      /** @var Quotations $quotation */
      $quotation = $_SESSION["wizard"]['quotation'];

      if ($quotation->stoneCategoryId == "") {
        MessageFlashCoordinator::addMessageAlert(__("Onbekende steen. Start een nieuwe offerte, de huidige offerte is ongeldig. (" . $quotation->stoneId . ")"));
        ResponseHelper::redirect(reconstructQueryAdd());
      }

      $errors = [];


      //userId ophalen voordat je prijs bepaald
      if ($quotation->userId == "") {
        $quotation->userId = $_SESSION['userObject']->userId;
        $quotation->companyId = $_SESSION['userObject']->companyId;
      }

      /** @var QuotationsExtra $quotation_extra */
      $quotation_extra = $_SESSION["wizard"]['quotation_extra'];
      $stone = Stones::find_by(["stoneId" => $quotation->stoneId]);
      $stone_size = StoneSizes::find_by(["sizeId" => $stone->sizeId]);
      $elements = $_SESSION["wizard"]['elements'];
      $mitres = [];
      if ($stone->isKeramiek() && $stone->isRaamdorpel()) {
        $mitres = AppModel::mapObjectIds(Mitres::getMitresByStoneId($quotation->stoneId), "mitreId");
      }
      else {
        $mitres = AppModel::mapObjectIds(Mitres::getMitresByStoneId(Config::get("MITRE_FALLBACK_STONE_ID")), "mitreId");
      }
      $extra_products = [];
      if (isset($_SESSION["wizard"]['extra_products'])) {
        $extra_products = $_SESSION["wizard"]['extra_products'];
      }

      if (isset($_POST["next"]) || isset($_POST["next_order"]) || isset($_POST["prev"])) {

        $quotation->customerNotes = trim($_POST["customerNotes"]);

        //bij een volgende post, zonder validatie naar vorige stap, maar wel elementen opslaan.
        if (isset($_POST["prev"])) {
          $_SESSION["wizard"]['quotation'] = $quotation;
          ResponseHelper::redirect(reconstructQuery(["step"]) . "step=5");
        }

        $prices = Quotations::getPrices($quotation_extra->getQuotationAltPriceYear("Y-m-d"), $quotation->userId, $stone);

        $company = ($_SESSION['userObject'] ?? null)?->company ?? false;
        $quotation_extra->setMaxMeasureElement($quotation, $company, $stone);

        /** @var OrderElements $element */
        foreach ($elements as $element) {
          $element->calculateValues($quotation, $quotation_extra, $stone_size, $stone);
          try {
            $element->calculatePrice($quotation, $prices, $stone_size, $stone);
          }
          catch (GsdException $e) {
            $errors[] = $e->getMessage();
          }
        }

        if (count($errors) == 0) {

          if ($prices && $prices['codeId'] != '') {
            $quotation->codeId = $prices['codeId'];
          }
          else {
            $quotation->codeId = 31;
          }
          $quotation->statusId = Status::STATUS_NEW;
          $quotation->quotationDate = date('Y-m-d');
          $quotation->priceDate = date('Y-m-d');
          $quotation->projectValue = 0;
          $quotation->meters = 0;
          $quotation->weight = 0;
          $quotation->stoneAmount = 0;
          $quotation->stonesFacilitated = 'false';
          $quotation->pickup = $quotation_extra->isPickup() ? 'true' : 'false';
          $quotation->zipcodeMap = str_replace(" ", "", $quotation->zipcode); //kopie
          if ($quotation->country == "") $quotation->country = 'NL';
          $quotation->internNotes = $quotation->customerNotes; //kopie
          if (trim($quotation->deliveryNotes) != "") {
            $quotation->dispatchAppointment = $quotation->deliveryNotes;
          }
          $quotation->createdVia = Quotations::CREATED_VIA_WIZARD;
          if ($stone_size->isOpmaatgemaakt() && isset($quotation->custom_stone)) {
            $quotation->productionNotes = "op maat gemaakt " . $quotation->custom_stone->depth . " x " . $quotation->custom_stone->thickness . "/" . $quotation->custom_stone->height . " D:" . $quotation->custom_stone->width_click . " E:" . $quotation->custom_stone->height_click;
            $quotation_extra->quoteInvoiceAlertFlag = 1;
            $quotation_extra->quoteInvoiceAlertInfo = "Op maat gemaakt";
          }
          if ($save) $quotation->save();
          //pd($quotation);

          $elementCount = 0;
          /** @var OrderElements $element */
          foreach ($elements as $element) {
            $element->quotationId = $quotation->quotationId;

            //extra controle op schuin einde. schuin einde heeft geen eindsteen
            if ($element->leftMitreId != null) {//links schuin, dan geen rechter eindsteen
              $element->leftEndstone = 0;
              $element->leftEndstoneGrooves = 0;
              $element->leftMitre = $mitres[$element->leftMitreId];
            }
            if ($element->rightMitreId != null) {//rechts schuin, dan geen rechter eindsteen
              $element->rightEndstone = 0;
              $element->rightEndstoneGrooves = 0;
              $element->rightMitre = $mitres[$element->rightMitreId];
            }
            //          pd($element->referenceName);

            $element->calculateValues($quotation, $quotation_extra, $stone_size, $stone);
            $element->calculatePrice($quotation, $prices, $stone_size, $stone);

            if ($save) {
              $element->save();
            }
            $elementCount += $element->amount;

            //          pd($element->elementLength);
            //          pd($element->elementLengthTotal);

            if (isset($element->windowsill)) {
              $element->windowsill->element_id = $element->elementId;
              $element->windowsill->save();
            }

            if (isset($element->order_element_sizes)) {
              foreach ($element->order_element_sizes as $oes) {
                $oes->element_id = $element->elementId;
                $oes->save();
              }
            }

          }

          if (isset($quotation->copyQuotationId)) {
            $copyQuotation = Quotations::getById($quotation->copyQuotationId);
            $nrparts = explode(".", $copyQuotation->quotationNumber);
            $quotation->quotationNumber = date("y") . '.' . $nrparts[1];
            $quotation->quotationVersion = Quotations::getNewVersion($copyQuotation);
          }
          elseif ($quotation->quotationNumber == "") { //nieuwe offerte
            $quotation->quotationNumber = date("y") . '.' . sprintf("%06d", $quotation->quotationId);
          }
          if ($quotation->quotationVersion == "") {
            $quotation->quotationVersion = 0;
          }

          $elementTotals = OrderElements::calculateTotals($elements);

          $quotation->projectValue = $elementTotals["projectValue"];
          $quotation->meters = round(($elementTotals["fullLength"] / 1000), 2);
          $quotation->metersMuch = $quotation->meters >= 90 ? 'true' : 'false';
          $quotation->weight = $elementTotals["weight"];
          $quotation->stoneAmount = $elementTotals["stoneAmount"];

          $quotation_extra->totalLeftEndStones = $elementTotals["totalLeftEndStones"];
          $quotation_extra->totalRightEndStones = $elementTotals["totalRightEndStones"];
          $quotation_extra->totalLeftEndStonesGrooves = $elementTotals["totalLeftEndStonesGrooves"];
          $quotation_extra->totalRightEndStonesGrooves = $elementTotals["totalRightEndStonesGrooves"];
          $quotation_extra->totalMiddlesStones = $elementTotals["totalMiddlesStones"];


          //address stuff
          if ($quotation_extra->addressDeliveryId != "" && $quotation_extra->addressDeliveryId != "NEW") {
            $crmAddress = CrmAddresses::find_by(["addressId" => $quotation_extra->addressDeliveryId]);
          }
          else {
            $crmAddress = CrmAddresses::getAddressObj($quotation);
            if ($crmAddress->addressId == "") {
              $crmAddress->longitude = null;
              $crmAddress->latitude = null;
              $crmAddress->save(); //nieuwe, even opslaan
            }
            $quotation_extra->addressDeliveryId = $crmAddress->addressId;
          }

          if ($crmAddress && $crmAddress->addressId != "" && $_SESSION['userObject']->lastDeliveryAddressId != $crmAddress->addressId) {
            $luser = SandboxUsers::getUserAndCompany($_SESSION['userObject']->userId);
            $luser->lastDeliveryAddressId = $crmAddress->addressId;
            if ($save) $luser->save();
            $_SESSION['userObject'] = $luser;
          }

          if ($_SESSION['userObject']->companyId != "") {
            $company = $_SESSION['userObject']->company;
            $quotation_extra->setMaxMeasureElement($quotation, $company, $stone);

            $quotation_extra->callToDelivery = $company->callToDelivery;
            $quotation_extra->executorTicket = $company->executorTicket;
            $quotation_extra->rackContainerReturn = $company->rackContainerReturn;

            $quotation->noRackQuotations = $company->noRack;
            $quotation->sendMailToClient = $company->sendMailToClient;
            $quotation->callOrEmailNotes = $company->callOrEmailNotes;
            $quotation->noContainerQuotations = $company->noContainer;
            $quotation->noRackNoContainerQuotations = $company->noRackNoContainer;
            $quotation->palletQuotations = $company->pallet;
            $quotation->toNumberQuotations = $company->toNumber;
            if (!$quotation->toNumberQuotations) {
              //meer dan 50 elementen dan op aan
              $number_config = Config::get('NUMBER_QUOTATIONS_THRESHOLDS');
              if ($elementCount >= $number_config["ELEMENTAMOUNT"]) {
                //er zijn meer dan (50) elementen, dan nummeren
                $quotation->toNumberQuotations = 1;
              }
              else {
                //zijn er elementen met minder dan 10cm lengte verschil, dan nummeren
                $lengths = [];
                foreach ($elements as $element) {
                  $lengths[] = $element->elementLength;
                }
                sort($lengths, SORT_NUMERIC);

                $elementsLessThen10cmDiff = false;
                $diff_min = $number_config["LENGTH_DIFFERENCE"];
                foreach ($lengths as $t => $length) {
                  if (isset($lengths[$t - 1])) {
                    $diff = $lengths[$t - 1] - $length;
                    if ($diff != 0 && $diff >= -1 * $diff_min && $diff <= $diff_min) {
                      $elementsLessThen10cmDiff = true;
                      break;
                    }
                  }
                }

                if ($elementsLessThen10cmDiff) {
                  $quotation->toNumberQuotations = 1;
                }

              }

            }
            $quotation->afhalenQuotations = $company->afhalen;

          }
          else {
            $quotation_extra->setMaxMeasureElement($quotation, false, $stone);
          }

          if (isset($_SESSION['register_referral'])) {
            $quotation_extra->supplier = ($_SESSION['register_referral'] == 'wienerberger' ? 'wienerberger' : 'stjoris');
          }
          $quotation_extra->stoneSizeWidth = $stone_size->width;
          $quotation_extra->seamColorId = 1;
          $quotation_extra->quotationId = $quotation->quotationId;

          $quotation->setMatting($quotation_extra, $prices);

          //        pd($quotation);
          //        pd($quotation_extra);

          if ($save) {
            $quotation->save();
            $quotation_extra->save();

            // Calculate element parts
            (new SplitCalculator($quotation, $quotation_extra))->run();

            //kleine order toeslag toevoegen als losse regel.
            $soa = new SetSmallOrderAddition($quotation, $elements, $extra_products);
            if($soa->hasSmallOrderAddition() && !$soa->containsSmallOrderAddition()) {
              $soaProjects = $soa->getProject();
              $extra_products[$soaProjects->product_id] = $soaProjects;
            }

            foreach ($extra_products as $extra_project) {
              if ($extra_project->size > 0) {
                $extra_project->quotationId = $quotation->quotationId;
                $extra_project->save();
              }
              elseif ($extra_project->projectId != "" && $extra_project->size == 0) {
                $extra_project->destroy();
              }
            }

            if (isset($quotation->custom_stone)) {
              $quotation->custom_stone->quotationId = $quotation->quotationId;
              $quotation->custom_stone->save();
            }

          }

          MailsFactory::sendTenderEmail($quotation, $elements);

          if ($stone_size->isOpmaatgemaakt()) {
            MailsFactory::sendOpmaatgemaakt($quotation);
          }
          else {
            MailsFactory::sendPriceValidationEmail($quotation, $quotation_extra, $elements, $stone);
          }

          if (isset($_POST["next_order"])) {
            //direct bestellen

            $payonline = false;
            if ($_SESSION['userObject']->isPrivate()) {
              if ($quotation->pickup == "true") {
                $quotation->freightCosts = 0;
              }
              else {
                $quotation->freightCosts = Config::get("QUOTATION_DEFAULT_FREIGHTCOSTS");
              }
              $total_price_excl = $quotation->getTotalPrice(false);
              if ($total_price_excl < 200) {
                //direct afrekenen met mollie
                $payonline = true;
              }
            }

            if ($payonline && !SandboxUsers::isAdmin()) {
              //online afrekenen, dan even via het bestellen scherm voor online afrekenen. Admin gaat niet online afrekenen.
              MessageFlashCoordinator::addMessageAlert(__("U heeft uw offerte per e-mail ontvangen. U kunt op deze pagina uw offerte direct omzetten in een bestelling."));
              ResponseHelper::redirect("?action=confirm&id=" . $quotation->quotationId);
            }


            $neededdays = Quotations::determineNeededProductiondays();
            $quotation->dueDate = date('Y-m-d', strtotime("+" . $neededdays . " DAYS"));
            $quotation->dueDateWeek = date('W', strtotime("+" . $neededdays . " DAYS"));

            $quotation->statusId = Status::STATUS_CHECKED; //akkoord
            $quotation->orderDate = date('Y-m-d');
            $quotation->productionDate = date('Y-m-d');
            $quotation->zipcodeMap = str_replace(" ", "", $quotation->zipcode);
            $quotation->save();

            //production order
            $productionorder = new ProductionOrder();
            $productionorder->quotationId = $quotation->quotationId;
            $productionorder->colorId = $stone->colorId;
            $productionorder->sizeId = $stone->sizeId;
            $productionorder->productionDate = $quotation->dueDate;
            $productionorder->save();

            MailsFactory::sendOrderMail($quotation);
            //          MailsFactory::sendMissingDataEmail($quotation);


          }

          ResponseHelper::redirect(reconstructQuery(["step"]) . "step=7&quotationId=" . $quotation->quotationId);

        }
      }


      $this->step = 6;
      $this->errors = $errors;
      $this->quotation = $quotation;
      $this->quotation_extra = $quotation_extra;
      $this->stone = $stone;
      $this->brand = StoneBrands::find_by(["brandId" => $quotation->brandId]);
      foreach ($elements as $element) {
        //waarden bereken, zodat we lengte kunnen bepalen.
        $element->calculateValues($quotation, $quotation_extra, $stone_size, $stone);
      }
      $elementTotals = OrderElements::calculateTotals($elements);
      $this->elements = $elements;
      $this->meters = round(($elementTotals["fullLength"] / 1000), 2);
      $this->extra_products = $extra_products;
    }


  }