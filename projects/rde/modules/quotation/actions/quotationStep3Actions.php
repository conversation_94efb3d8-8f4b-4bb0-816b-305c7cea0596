<?php

  trait quotationStep3Actions {


    public function executeWizardstep3() {
      if (!isset($_SESSION["wizard"]['quotation']) || !isset($_SESSION["wizard"]['elements'])) {
        ResponseHelper::redirect($this->wizardurl);
      }
      /** @var Quotations $quotation */
      $quotation = $_SESSION["wizard"]['quotation'];
      $elements = $_SESSION["wizard"]['elements'];

      //controleer of er wel verstek is
      if (!$this->hasVerstek($quotation, $elements)) {
        ResponseHelper::redirect(reconstructQuery(["step"]) . "step=4");
      }

      if (isset($_GET["json"])) {
        if (isset($_POST["next"]) || isset($_POST["prev"])) {
          $this->step3Post();
        }
        else {
          $this->step3Get();
        }
      }

      $this->step = 3;
      $this->quotation = $_SESSION["wizard"]['quotation'];
    }

    public function step3Get() {

      if (!isset($_SESSION["wizard"]['quotation']) || !isset($_SESSION["wizard"]['elements'])) {
        ResponseHelper::responseError("Geen offerte. U word doorgestuurd naar stap 1.", $this->wizardurl);
      }

      /** @var Quotations $quotation */
      $quotation = $_SESSION["wizard"]['quotation'];
      $quotation_extra = $_SESSION["wizard"]['quotation_extra'];
      $elements = $_SESSION["wizard"]['elements'];
      $stone = Stones::find_by(["stoneId" => $quotation->stoneId]);
      $stone_size = StoneSizes::find_by(["sizeId" => $quotation->sizeId]);
      $showGemeten = true;

      if ($stone->isMuurafdekker() || $stone->isSpekband()) {
        //worden berekend a.d.h.v. muurbreedte
        $quotation->heartClickSize = 0;
        $showGemeten = false;
      }

      $mitres = [];
      if ($stone->isKeramiek() && $stone->isRaamdorpel()) {
        $mitres = AppModel::mapObjectIds(Mitres::getMitresByStoneId($quotation->stoneId), "mitreId");
      }
      else {
        $mitres = AppModel::mapObjectIds(Mitres::getMitresByStoneId(Config::get("MITRE_FALLBACK_STONE_ID")), "mitreId");
      }

      $mitresSelect = [
        '90° hoek'  => [],
        '135° hoek' => [],
        'Overige'   => [],
      ];
      foreach ($mitres as $mitre) {
        if ($stone->isKeramiek() && $stone->isMuurafdekker() && $mitre->angle > 90) {
          continue;
        }
        if ($mitre->angle == 45) {
          $mitresSelect['90° hoek'][] = $mitre;
        }
        elseif ($mitre->angle == 67.5) {
          $mitresSelect['135° hoek'][] = $mitre;
        }
        else {
          $mitresSelect['Overige'][] = $mitre;
        }
      }
      //controleer of deze mitres wel bij deze steen horen. Zo nee, terug naar default.
      foreach ($elements as $element) {
        if ($element->leftMitreId != "" && !isset($mitres[$element->leftMitreId])) { //mitreId hoort niet bij deze steen. Naar 45graden
          $element->leftMitreId = $mitresSelect['90° hoek'][0]->mitreId;
        }
        if ($element->rightMitreId != "" && !isset($mitres[$element->rightMitreId])) { //mitreId hoort niet bij deze steen. Naar 45graden
          $element->rightMitreId = $mitresSelect['90° hoek'][0]->mitreId;
        }
      }

      $data = new stdClass();
      $data->step = 3;
      $data->quotation = $quotation;
      $data->quotation_extra = $quotation_extra;
      $data->quotationExtraSpacing = $stone->getExtraSpacing();
      $data->stone = $stone;
      $data->stone_size = $stone_size;
      $data->elements = $elements;
      $data->mitres = AppModel::mapObjectIds($mitres, 'mitreId');
      $data->mitresSelect = $mitresSelect;
      $data->showGemeten = $showGemeten;
      $data->errors = [];
      ResponseHelper::responseSuccess($data);
    }

    public function step3Post() {
      if (!isset($_SESSION["wizard"]['quotation']) || !isset($_SESSION["wizard"]['elements'])) {
        ResponseHelper::responseError("Geen offerte. U word doorgestuurd naar stap 1.", $this->wizardurl);
      }

      $response = [];
      $response["errors"] = [];

      /** @var Quotations $quotation */
      $quotation = $_SESSION["wizard"]['quotation'];
      $elements = $_SESSION["wizard"]['elements'];
      $stone = Stones::find_by(["stoneId" => $quotation->stoneId]);

      if ($stone->isSpekband() || $stone->isMuurafdekker()) {
        //worden berekend a.d.h.v. muurbreedte
        $quotation->heartClickSize = 0;
      }
      if (isset($_POST["heartClickSize"]) && $_POST["heartClickSize"] != "") {
        $quotation->heartClickSize = $_POST["heartClickSize"];
      }
      if (isset($quotation->heartClickSize) && $quotation->heartClickSize != "") {
        foreach ($elements as $element) {
          //heartclick size altijd overnemen naar alle elementen, zodat er geen problemen onstaat bij het verwijderen e.d.
          $element->heartClickSize = $quotation->heartClickSize;
        }
      }

      if (isset($_POST["elements"]) && $_POST["elements"] != "") {
        foreach ($_POST['elements'] as $key => $elementin) {
          $c_element = $elements[$key];
          $c_element->inputLength = trim($elementin['inputLength']);
          $c_element->leftMitreId = isset($elementin['leftMitreId']) ? $elementin['leftMitreId'] : null;
          $c_element->rightMitreId = isset($elementin['rightMitreId']) ? $elementin['rightMitreId'] : null;
        }
      }

      //bij een volgende post, zonder validatie naar vorige stap, maar wel elementen opslaan.
      if (isset($_POST["prev"])) {
        $_SESSION["wizard"]['quotation'] = $quotation;
        $_SESSION["wizard"]['elements'] = $elements;
        ResponseHelper::responseSuccess($response, reconstructQuery(["step", "json"]) . "step=2");
      }

      if (count($response["errors"]) > 0) {
        ResponseHelper::responseSuccess($response);
      }

      $_SESSION["wizard"]['quotation'] = $quotation;
      $_SESSION["wizard"]['elements'] = $elements;
      ResponseHelper::responseSuccess($response, reconstructQuery(["step", "json"]) . "step=4");


    }


  }