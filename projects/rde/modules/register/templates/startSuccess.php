<section>

  <div class="contenttxt">
    <h1>Registreren
    <?php if(isset($_SESSION['register_referral'])) echo " voor ".($_SESSION['register_referral'] =='wienerberger'?'Wienerberger':'St. Joris') ?>
    </h1>
    <?php echo process_text($page->content->content1); ?>
    <br/>
  </div>

  <form method="post" id="startform">

    <?php writeErrors($errors, true) ?>

    <div class="form-row">
      <label for="email" class="col-4 col-form-label">
        <?php echo __("E-mail") ?>
        <span class="form-arterisk">*</span>
      </label>
      <div class="col-4-3">
        <input type="email" class="form-input" name="email" value="<?php echo escapeForInput($user->email) ?>" size="40" maxlength="150" placeholder="<?php echo __("E-mail") ?>" required/>
      </div>
    </div>

    <div class="form-row">
      <label for="password1" class="col-4 col-form-label"><?php echo __("Wachtwoord") ?> <span class="form-arterisk">*</span></label>
      <div class="col-4-3">
        <input type="password" class="form-input" name="password1" id="password1" value="<?php echo escapeForInput($user->password) ?>" size="30" maxlength="50" placeholder="<?php echo __("Wachtwoord") ?>" required/>
      </div>
    </div>

    <div class="form-row">
      <label for="password2" class="col-4 col-form-label"><?php echo __("Wachtwoord bevestig") ?> <span class="form-arterisk">*</span></label>
      <div class="col-4-3">
        <input type="password" class="form-input" name="password2" id="password2" value="<?php echo escapeForInput($user->password) ?>" size="30" maxlength="50" placeholder="<?php echo __("Wachtwoord bevestig") ?>" required/>
      </div>
    </div>

    <div class="form-row">
      <label for="private" class="col-4 col-form-label"><?php echo __("Bestellen als") ?> <span class="form-arterisk">*</span> </label>
      <div class="col-4-3 ">
        <label style="float: left;">
          <input type="radio" class="form-radio form-check-inline organ_type" name="private" value="false" required <?php writeIfCheckedVal($user->private,'false') ?>/>
          <?php echo __("Bedrijf") ?>
        </label>
        <label style="float: left;padding-left: 15px;">
          <input type="radio" class="form-radio form-check-inline organ_type" name="private" value="true" required <?php writeIfCheckedVal($user->private,'true') ?>/>
          <?php echo __("Particulier") ?>
        </label>
      </div>
    </div>

    <div class="form-row <?php if($user->isPrivate()): ?> hidden <?php endif; ?>" id="companyName">
      <label for="organ_name" class="col-4 col-form-label"><?php echo __("Bedrijfsnaam") ?></label>
      <div class="col-4-3">
        <input type="text" class="form-input" name="companyName" id="companyName" value="<?php echo displayAsHtml($user->companyName) ?>" size="30" maxlength="50" placeholder="<?php echo __("Bedrijfsnaam") ?>"/>
      </div>
    </div>
    <div class="form-row">
      <label for="gender" class="col-4 col-form-label"><?php echo __("Aanhef") ?> <span class="form-arterisk">*</span> </label>
      <div class="col-4-3">
        <label style="float:left;">
          <input type="radio" class="form-radio form-check-inline" name="gender" value="male" required <?php writeIfCheckedVal($user->gender, 'male') ?>/>
          <?php echo __("Dhr.") ?>
        </label>
        <label style="float: left;padding-left: 15px;">
          <input type="radio" class="form-radio form-check-inline" name="gender" value="female" required <?php writeIfCheckedVal($user->gender, 'female') ?>/>
          <?php echo __("Mevr.") ?>
        </label>
      </div>
    </div>

    <div class="form-row">
      <label for="firstName" class="col-4 col-form-label"><?php echo __("Voornaam") ?> <span class="form-arterisk">*</span> </label>

      <div class="col-4-3">
        <input type="text" class="form-input" name="firstName" placeholder="<?php echo __("Voornaam") ?>" value="<?php echo displayAsHtml($user->firstName) ?>" required/>
      </div>
    </div>

    <div class="form-row">
      <label for="lastName" class="col-4 col-form-label"><?php echo __("Achternaam") ?> <span class="form-arterisk">*</span> </label>

      <div class="col-4-3">
        <input type="text" class="form-input" name="lastName" placeholder="<?php echo __("Achternaam") ?>" value="<?php echo displayAsHtml($user->lastName )?>" required/>
      </div>
    </div>

    <div class="form-row">
      <label for="phone" class="col-4 col-form-label"><?php echo __("Telefoonnummer") ?> <span class="form-arterisk">*</span> </label>
      <div class="col-4-3">
        <input type="text" class="form-input" name="phone" value="<?php echo displayAsHtml($user->phone) ?>" size="25" maxlength="15" placeholder="<?php echo __("Telefoonnummer") ?>" required/>
      </div>
    </div>

    <div class="form-row">
      <label for="mobile" class="col-4 col-form-label"><?php echo __("Mobiel nummer") ?></label>
      <div class="col-4-3">
        <input type="text" class="form-input" name="mobile" value="<?php echo displayAsHtml($user->mobile) ?>" size="25" maxlength="15" placeholder="<?php echo __("Mobiel nummer") ?>"/>
      </div>
    </div>

    <div class="form-row">
      <label for="mobile" class="col-4 col-form-label"><?php echo __("Factuur e-mailadres") ?></label>
      <div class="col-4-3">
        <input type="email" class="form-input" name="invoice_email" value="<?php echo displayAsHtml($user->invoice_email) ?>" placeholder="<?php echo __("Factuur e-mailadres") ?>"/>
      </div>
    </div>

    <div class="form-row">
      <label class="col-4 col-form-label mobileshow"><b><?php echo __("Adresgegevens") ?></b></label>
      <div class="col-4-3">
        Wanneer u gaat bestellen kunt u optioneel een ander afleveradres opgeven.
      </div>
    </div>

    <div class="form-row">
      <label class="col-4 col-form-label"><?php echo __("Postcode + nummer") ?> <span class="form-arterisk">*</span></label>
      <div class="col-4"  style="margin-right: 15px;">
        <input type="text" class="form-input" name="zipcode" id="zipcode" value="<?php echo displayAsHtml($user->zipcode) ?>" placeholder="<?php echo __("Postcode") ?>" required/>
      </div>
      <div class="col-4" style="margin-right: 15px;">
        <input type="number" class="form-input" name="nr" id="nr" value="<?php echo displayAsHtml($user->nr) ?>" placeholder="<?php echo __("Huisnummer") ?>" required maxlength="5"/>
      </div>
      <div class="col-4" style="width: calc(25% - 30px);">
        <input type="text" class="form-input" name="extension" value="<?php echo displayAsHtml($user->extension) ?>" placeholder="<?php echo __("Toevoeging") ?>" maxlength="10" />
      </div>
    </div>

    <div class="form-row">
      <label class="col-4 col-form-label"><?php echo __("Plaats + straatnaam") ?> <span class="form-arterisk">*</span></label>
      <div class="col-4" style="margin-right: 15px;">
        <input type="text" class="form-input" name="domestic" id="domestic" value="<?php echo displayAsHtml($user->domestic) ?>" placeholder="<?php echo __("Plaats") ?>" readonly/>
      </div>
      <div class="col-4" style="width: calc(50% - 15px);">
        <input type="text" class="form-input" name="street" id="street" value="<?php echo displayAsHtml($user->street) ?>" placeholder="<?php echo __("Straatnaam") ?>" readonly/>
      </div>
    </div>

    <div class="form-row">
      <label class="col-4 col-form-label"><?php echo __("Land") ?> <span class="form-arterisk">*</span></label>
      <div class="col-4-3">
        <select name="country" id="country" required>
          <option value="BE" <?php writeIfSelectedVal($user->country,'BE') ?>>België</option>
          <option value="DE" <?php writeIfSelectedVal($user->country,'DE') ?>>Duitsland</option>
          <option value="NL" <?php writeIfSelectedVal($user->country,'NL') ?>>Nederland</option>
        </select>
      </div>
    </div>

    <div class="form-row">
      <div class="col-4 responsive-hide">&nbsp;</div>
      <div class="col-4-3">
        <input type="submit" value="<?php echo __("Registreren") ?>" class="btn btn-primary" name="registeren" id="registeren"/>
      </div>
    </div>

  </form>

</section>

<script type="text/javascript">
  $(document).ready(function () {


    $('.organ_type').on("click", function () {
      if ($(this).val() == 'false') {
        $('#companyName').removeClass('hidden');
      }
      else {
        $('#companyName').addClass('hidden');
      }
    });
    $('#afleveradres').on("click", function () {
      if ($(this).prop("checked")) {
        $('.afleveradres').removeClass("hidden");
      }
      else {
        $('.afleveradres').addClass("hidden");
      }
    });

    jQuery.extend(jQuery.validator.messages, {
      required: "<?php echo __("Dit veld is verplicht") ?>",
      email: "<?php echo __("Voer een geldig emailadres in.") ?>",
      equalTo: "<?php echo __("Voer 2 keer dezelfde waarde in.") ?>",
      minlength: "<?php echo __("Voer minimaal {0} karakters in." ) ?>"
    });

    $('#startform').validate( {
      rules: {
        password1: {
          minlength: 8
        },
        password2: {
          equalTo: "#password1"
        }
      }
    });

    //----------- postcode api javascript - start --------------

    $('#country').on("change", function () {
      //only use postcode api in netherlands
      if($("#country").val()=="NL") {
        $('#street,#domestic').prop("readonly",true);
        $("#zipcode").change();
      }
      else {
        $('#street,#domestic').prop("readonly",false);
      }
    });

    $('#zipcode,#nr').on("change", function () {
      if($("#country").val()!="NL") { //only use postcode api in netherlands
        return;
      }
      $.getJSON("<?php echo reconstructQuery() ?>&action=postcodeapi&zipcode=" + $("#zipcode").val()+"&nr=" + $("#nr").val(),
        function (data) {
          if (data.success) {
            if (data.success=="open") {
              //no credits at postcode api, customer can enter the info himself
              $('#street,#domestic').prop("readonly",false);
            }
            else {
              $("#street").val(data.data.street);
              $("#domestic").val(data.data.city);
            }
          }
          else {
            $("#street").val("");
            $("#domestic").val("");
          }
        });
    });

    <?php if(isset($errors["street"]) || isset($errors["domestic"])): //on error customer may enter street and city ?>
      $('#street,#domestic').prop("readonly",false);
    <?php endif; ?>

    //----------- postcode api javascript - end --------------


  });
</script>