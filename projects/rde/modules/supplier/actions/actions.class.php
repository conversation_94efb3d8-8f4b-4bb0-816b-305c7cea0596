<?php

  use Gsd\DataTable\DataTable;
  use Gsd\Form\Elements\A;

  class supplierRdeActions extends gsdActions {

    public function executeOrders() {
      $this->statusIds = [Status::STATUS_CHECKED];
      $this->isInvoices = false;
      $this->list();
      $this->template = "listSuccess.php";
    }

    public function executeProduction() {
      $this->statusIds = [Status::STATUS_PREPARED];
      $this->isInvoices = false;
      $this->list();
      $this->template = "listSuccess.php";
    }

    public function executePickup() {
      $this->statusIds = [Status::STATUS_IN_PRODUCTION];
      $this->isInvoices = false;
      $this->list();
      $this->template = "listSuccess.php";
    }


    private function list() {

      if (!isset($_SESSION['size_search'])) $_SESSION['size_search'] = '';
      if (!isset($_SESSION['size_brand'])) $_SESSION['size_brand'] = '';
      if (isset($_POST['go'])) {
        $_SESSION['size_search'] = trim($_POST['size_search']);
        $_SESSION['size_brand'] = trim($_POST['size_brand']);
        ResponseHelper::redirect(reconstructQuery());
      }

      $filt = "SELECT * FROM " . Quotations::getTablename();
      $filt .= " JOIN " . QuotationsExtra::getTablename() . " ON quotations_extra.quotationId=quotations.quotationId  ";
      $filt .= "WHERE NOT supplier_show IS NULL AND supplier_show!='' ";
      $filt .= "AND brandId IN (" . implode(",", StoneBrands::getBretiBrandIds()) . ") ";
      $filt .= " AND statusId>=30 AND statusId<=40  ";
      if ($_SESSION['size_brand'] != "") {
        $filt .= " AND brandId=" . $_SESSION['size_brand'] . " ";
      }
      if ($_SESSION['size_search'] != "") {
        $searchval = escapeForDB($_SESSION['size_search']);
        $filt .= " AND (";
        $filt .= " quotationNumber LIKE '%" . $searchval . "%' ";
        $filt .= ")";
      }

      $result = DBConn::db_link()->query($filt);

      $quotationIds = [];
      $quotationsGrouped = [];
      while ($row = $result->fetch_row()) {
        $quotation = new Quotations();
        $quotation->hydrate($row);

        $breti_quotation = BretiQuotation::find_by(['quotation_id' => $quotation->quotationId]);
        if ($breti_quotation) continue;

        $quotation->quotation_extra = new QuotationsExtra();
        $quotation->quotation_extra->hydrate($row, count(Quotations::columns));
        if (!isset($quotationsGrouped[$quotation->quotation_extra->addressDeliveryId])) {
          $quotationsGrouped[$quotation->quotation_extra->addressDeliveryId] = [
            "name"    => $quotation->quotation_extra->addressDeliveryId,
            "items"   => [],
            "duedate" => intval($quotation->getDueDate("U")),
          ];
        }
        if (intval($quotation->getDueDate("U")) < time()) {
          $quotationsGrouped[$quotation->quotation_extra->addressDeliveryId]["duedate"] = intval($quotation->getDueDate("U"));
        }
        $quotationsGrouped[$quotation->quotation_extra->addressDeliveryId]["items"][] = $quotation;
        $quotationIds[$quotation->quotationId] = $quotation->quotationId;
      }

      //letters toekennen
//    $letters = preg_split('#(?<=.)(?=.)#s', "ABCDEFGHIJKLMNOPQRSTUVWXYZ");
//    $letterKey = 0;
//    foreach($quotationsGrouped as $k=>$grouped) {
//      if(count($grouped["items"])>1) {
//        $quotationsGrouped[$k]["name"] = $letters[$letterKey];
//        $letterKey++;
//      };
//    }

      //controlleren of items uit deze groep mogen. Hierdoor houdenw de letters gelijk over de tablladen 1,2,3
//      dumpe($quotationsGrouped);
      foreach ($quotationsGrouped as $k => $grouped) {
        foreach ($grouped["items"] as $qk => $quotation) {
          if (!in_array($quotation->statusId, $this->statusIds)) {
            unset($quotationsGrouped[$k]["items"][$qk]);
          }
        }
        if (count($quotationsGrouped[$k]["items"]) == 0) {
          unset($quotationsGrouped[$k]);
        }
      }

      usort($quotationsGrouped, function ($a, $b) {
        return $a["duedate"] - $b["duedate"];
      });

      $routesGpsBuddyRde = GpsbuddyRde::find_all_by(["quotationId" => $quotationIds]);
      $routesRde = AppModel::mapObjectIds($routesGpsBuddyRde, "routeId");
      $routesIds = ArrayHelper::getValuesOfNestedObjectsAsArray($routesGpsBuddyRde, "routeId");
      $routes = GpsbuddyRoutes::find_all_by(["routeId" => $routesIds]);
      $quotationRoutes = [];

//      foreach ($routes as $route) {
//        $lRouteRde = $routesRde[$route->routeId];
//        foreach ($routesGpsBuddyRde as $gpsbuddyRde) {
//          $quotationRoutes[$gpsbuddyRde->quotationId] = $route;
//        }
//      }

      foreach ($routesGpsBuddyRde as $gpsbuddyRde) {
        $quotationRoutes[$gpsbuddyRde->quotationId] = GpsbuddyRoutes::find_by(['routeId' => $gpsbuddyRde->routeId]);
      }

      $this->quotationRoutes = $quotationRoutes;
      $this->quotationsGrouped = $quotationsGrouped;
      $this->brands = AppModel::mapObjectIds(StoneBrands::find_all_by(["brandId" => StoneBrands::getBretiBrandIds()], "ORDER BY displayOrder"), "brandId");

    }


    public function executeDelivered() { //geleverd terug naar rde

      if (!isset($_SESSION['size_search'])) $_SESSION['size_search'] = '';
      if (!isset($_SESSION['size_brand'])) $_SESSION['size_brand'] = '';
      if (isset($_POST['go'])) {
        $_SESSION['size_search'] = trim($_POST['size_search']);
        $_SESSION['size_brand'] = trim($_POST['size_brand']);
        ResponseHelper::redirect(reconstructQuery());
      }

      $filt = "SELECT * FROM " . Quotations::getTablename() . " ";
      $filt .= "JOIN " . QuotationsExtra::getTablename() . " ON quotations_extra.quotationId=quotations.quotationId  ";
      $filt .= "WHERE NOT supplier_show IS NULL AND supplier_show!='' ";
      $filt .= "AND brandId IN (" . implode(",", StoneBrands::getBretiBrandIds()) . ") ";
      $filt .= "AND statusId>=" . Status::STATUS_PRODUCED . " AND statusId<" . Status::STATUS_SPLIT . " ";
      if ($_SESSION['size_brand'] != "") {
        $filt .= " AND brandId=" . $_SESSION['size_brand'] . " ";
      }
      if ($_SESSION['size_search'] != "") {
        $searchval = escapeForDB($_SESSION['size_search']);
        $filt .= "AND (";
        $filt .= "quotationNumber LIKE '%" . $searchval . "%' ";
        $filt .= ")";
      }
      $filt .= "ORDER BY produceDate DESC ";
      $filt .= "LIMIT 100 ";

      $result = DBConn::db_link()->query($filt);

      $quotations = [];
      while ($row = $result->fetch_row()) {
        $quotation = new Quotations();
        $quotation->hydrate($row);
        $quotation->quotation_extra = new QuotationsExtra();
        $quotation->quotation_extra->hydrate($row, count(Quotations::columns));
        $quotations[] = $quotation;
      }
      $this->quotations = $quotations;
      $this->brands = AppModel::mapObjectIds(StoneBrands::find_all_by(["brandId" => StoneBrands::getBretiBrandIds()], "ORDER BY displayOrder"), "brandId");

    }

    public function executeDeliverednew() {

      if (!DEVELOPMENT) {
        if (!isset($_SESSION['size_search'])) $_SESSION['size_search'] = '';
        if (!isset($_SESSION['size_brand'])) $_SESSION['size_brand'] = '';
        if (isset($_POST['go'])) {
          $_SESSION['size_search'] = trim($_POST['size_search']);
          $_SESSION['size_brand'] = trim($_POST['size_brand']);
          ResponseHelper::redirect(reconstructQuery());
        }

        $filt = "SELECT * FROM " . Quotations::getTablename() . " ";
        $filt .= "JOIN " . QuotationsExtra::getTablename() . " ON quotations_extra.quotationId=quotations.quotationId  ";
        $filt .= "WHERE NOT supplier_show IS NULL AND supplier_show!='' ";
        $filt .= "AND brandId IN (" . implode(",", StoneBrands::getBretiBrandIds()) . ") ";
        $filt .= "AND statusId>=" . Status::STATUS_PRODUCED . " AND statusId<" . Status::STATUS_SPLIT . " ";
        if ($_SESSION['size_brand'] != "") {
          $filt .= " AND brandId=" . $_SESSION['size_brand'] . " ";
        }
        if ($_SESSION['size_search'] != "") {
          $searchval = escapeForDB($_SESSION['size_search']);
          $filt .= "AND (";
          $filt .= "quotationNumber LIKE '%" . $searchval . "%' ";
          $filt .= ")";
        }
        $filt .= "ORDER BY produceDate DESC ";
        $filt .= "LIMIT 100 ";

        $result = DBConn::db_link()->query($filt);

        $quotations = [];
        while ($row = $result->fetch_row()) {
          $quotation = new Quotations();
          $quotation->hydrate($row);
          $quotation->quotation_extra = new QuotationsExtra();
          $quotation->quotation_extra->hydrate($row, count(Quotations::columns));
          $quotations[] = $quotation;
        }
        $this->quotations = $quotations;
        $this->brands = AppModel::mapObjectIds(StoneBrands::find_all_by(["brandId" => StoneBrands::getBretiBrandIds()], "ORDER BY displayOrder"), "brandId");
      }
      else {
        if (!isset($_SESSION['years'])) $_SESSION['years'] = '';
        if (isset($_POST['go'])) {
          $_SESSION['years'] = trim($_POST['years']);
          ResponseHelper::redirect(reconstructQuery());
        }

        $filt = "SELECT * FROM " . BretiInvoice::getTablename() . " ";

        $filt .= 'WHERE 1 ';
        if ($_SESSION['years'] != "") {
          $filt .= " AND YEAR(breti_invoice_date) = " . $_SESSION['years'] . " ";
        }

        $filt .= "ORDER BY breti_invoice_date DESC ";
        $filt .= 'LIMIT 100 ';

        $result = DBConn::db_link()->query($filt);

        $breti_invoices = [];
        while ($row = $result->fetch_row()) {
          $column_counter = 0;
          $breti_invoice = (new BretiInvoice())->hydrateNext($row, $column_counter);
          $breti_quotations = BretiQuotation::find_all_by(['breti_invoice_id' => $breti_invoice->id]);

          $quotations = [];
          $quotation_ids = [];
          $quotations_count = 0;
          foreach ($breti_quotations as $breti_quotation) {
            $quotation_ids[] = $breti_quotation->quotation_id;
            $quotations_count++;

            $quotation = Quotations::find_by(['quotationId' => $breti_quotation->quotation_id]);

            $quotation_extra = QuotationsExtra::find_by(['quotationId' => $quotation->quotationId]);
            $brands = AppModel::mapObjectIds(StoneBrands::find_all_by(["brandId" => StoneBrands::getBretiBrandIds()], "ORDER BY displayOrder"), "brandId");
            $stone = Stones::find_by(['stoneId' => $quotation->stoneId]);

            if ($stone) {
              $size = StoneSizes::find_by(["sizeId" => $stone->sizeId]);
            }

            $quotation->quotation_extra = $quotation_extra;
            $quotation->brands = $brands;
            $quotation->size = $size;
            $breti_quotation->quotation = $quotation;
            $quotations[] = $breti_quotation;
          }

          $breti_invoice->breti_quotations = $quotations;
          $breti_invoice->quotation_ids = $quotation_ids;
          $breti_invoice->count = $quotations_count;
          $breti_invoices[] = $breti_invoice;
        }

        $invoice_total = 0;
        foreach ($breti_invoices as $invoice) {
          $invoice_total += $invoice->total_project_value;
        }

        $this->invoice_total = $invoice_total;
        $this->breti_invoices = $breti_invoices;
        $this->brands = AppModel::mapObjectIds(StoneBrands::find_all_by(["brandId" => StoneBrands::getBretiBrandIds()], "ORDER BY displayOrder"), "brandId");
      }


    }

    public function executeDownloadpdf() {
      $quotation = Quotations::getById($_GET["id"]);
      if (!in_array($quotation->brandId, StoneBrands::getBretiBrandIds())) {
        MessageFlashCoordinator::addMessageAlert("Dit is geen Breti bestelling!");
        ResponseHelper::redirect(reconstructQueryAdd());
      }

      if (DEVELOPMENT) {
        $pdf = new ProductionReceiptPdf($quotation->quotationId);
        $pdf->setSupplierExternal(true);
        $pdf->generate();
      }
      else {
        $filepath_gen = CargoReceipt::getProductionstaatPdf([$quotation->quotationId], true);
        $filename_new = $quotation->getQuotationNumberFull() . '_productiestaat.pdf';

        header('Content-Type: application/pdf');
        header('Content-Disposition: inline; filename="' . $filename_new . '"');
        header('Cache-Control: private, max-age=0, must-revalidate');
        header('Pragma: public');
        echo file_get_contents($filepath_gen);
      }
      ResponseHelper::exit();
    }

    public function executeDownloadbretipdf() {
      $quotation_ids = $_GET['quotation_ids'];

      if (DEVELOPMENT) {
        $pdf = new BretiProductionReceiptPdf($quotation_ids);
        $pdf->setSupplierExternal(true);
        $pdf->generate();
      }
      else {
        $filepath_gen = CargoReceipt::getProductionstaatPdf([$quotation->quotationId], true);
        $filename_new = $quotation->getQuotationNumberFull() . '_productiestaat.pdf';

        header('Content-Type: application/pdf');
        header('Content-Disposition: inline; filename="' . $filename_new . '"');
        header('Cache-Control: private, max-age=0, must-revalidate');
        header('Pragma: public');
        echo file_get_contents($filepath_gen);
      }
      ResponseHelper::exit();
    }

    public function executeSetinproduction() {
      $quotation = Quotations::getById($_GET["id"]);

      if ($quotation->statusId == Status::STATUS_CHECKED) {
        $quotation->statusId = Status::STATUS_PREPARED;
        $quotation->save();
      }

      MessageFlashCoordinator::addMessage("Bestelling " . $quotation->getQuotationNumberFull() . " gemarkeerd als 'In productie'");
      ResponseHelper::redirect(reconstructQueryAdd());
    }

    public function executeSetpickup() {
      $quotation = Quotations::getById($_GET["id"]);
      if (!in_array($quotation->brandId, StoneBrands::getBretiBrandIds())) {
        MessageFlashCoordinator::addMessageAlert("Dit is geen Breti bestelling!");
        ResponseHelper::redirect(reconstructQueryAdd());
      }
      if (!($quotation->statusId == Status::STATUS_CHECKED || $quotation->statusId == Status::STATUS_PREPARED)) {
        MessageFlashCoordinator::addMessageAlert("Bestelling heeft niet de juiste status. " . $quotation->statusId);
        ResponseHelper::redirect(reconstructQueryAdd());
      }
      $quotation->statusId = Status::STATUS_IN_PRODUCTION;
      $quotation->save();

      MailsFactory::sendBretiDoneEmail($quotation);

      MessageFlashCoordinator::addMessage("Bestelling " . $quotation->getQuotationNumberFull() . " gemarkeerd als 'Afhalen'");
      ResponseHelper::redirect(reconstructQueryAdd());
    }


    public function executeResetpickup() {
      $quotation = Quotations::getById($_GET["id"]);
      if (!in_array($quotation->brandId, StoneBrands::getBretiBrandIds())) {
        MessageFlashCoordinator::addMessageAlert("Dit is geen Breti bestelling!");
        ResponseHelper::redirect(reconstructQueryAdd());
      }
      if ($quotation->statusId != Status::STATUS_IN_PRODUCTION) {
        MessageFlashCoordinator::addMessageAlert("Bestelling heeft niet de juiste status. " . $quotation->statusId);
        ResponseHelper::redirect(reconstructQueryAdd());
      }
      $quotation->statusId = Status::STATUS_PREPARED;
      $quotation->save();

      $message = "Beste,<br/><br/>";
      $message .= "Bestelling  " . $quotation->getQuotationNumberFull() . " kan <b>NIET</b> worden opgehaald.<br/>";
      $message .= "Breti heeft de status teruggezet.<br/><br/>";
      $message .= "<i>Dit bericht is automatisch gegenereerd.</i>";

      GsdMailer::build(MAIL_BART, "Breti bestelling NIET ophalen: " . $quotation->getQuotationNumberFull(), $message)->send();

      MessageFlashCoordinator::addMessage("Bestelling " . $quotation->getQuotationNumberFull() . " teruggezet naar 'In productie'.");
      ResponseHelper::redirect(reconstructQueryAdd());
    }

    public function executeInvoices() {
      $this->statusIds = [Status::STATUS_PRODUCED];
      $this->isInvoices = true;
      $this->list();
      $this->template = "listSuccess.php";

      if (isset($_POST['invoice'])) {

        $quotation_ids = [];
        foreach ($_POST as $key => $val) {
          if (str_starts_with($key, 'check_for_invoice_')) {
            $quotation_ids[] = $val;
          }
        }

        if (!empty($quotation_ids)) {
          ResponseHelper::redirect(reconstructQueryAdd(['pageId', 'action']) . 'action=create_breti_invoice&quotation_ids=' . implode(',', $quotation_ids));
        }
        else {
          $_SESSION['flash_message_red'] = "Geen offertes geselecteerd";
          ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
        }
      }
    }

    public function createInvoicesListfilters() {

      $dataTable = new DataTable("breti_invoices_datatable");
      $dataTable->setRequestUrl(reconstructQueryAdd(['pageId']) . "action=invoiceslistajax");
      $dataTable->addColumnHelper("addressDeliveryId", "Leveringsnummer");
      $dataTable->addColumnHelper("supplier_time", "Leverweek klant");
      $dataTable->addColumnHelper("productionDate", "Opdrachtdatum");
      $dataTable->addColumnHelper("quotationNumber", "Offertenummer");
      $dataTable->addColumnHelper("brand_name", "Merk/materiaal");
      $dataTable->addColumnHelper("stone_name", "Model");
      $dataTable->addColumnHelper("meters", "Meters");
      $dataTable->addColumnHelper("projectValue", "Prijs");
      $dataTable->addColumnHelper("surface", "Oppervlakte");
      $dataTable->addColumnHelper("actions", "Acties");
      $dataTable->addColumnHelper("produceDate", "Geleverd op");
      $dataTable->setDefaultSort("produceDate", "Desc");
      $dataTable->setRowSelection(true);

      $dataTable->addSearchInput();
      $dataTable->addSearchReset();

      $dataTable->handleRequest($_POST);

      $this->dataTable = $dataTable;
    }

    public function executeInvoiceslistajax() {
      $this->createInvoicesListfilters();

      $filt = " JOIN " . QuotationsExtra::getTablename() . " ON quotations_extra.quotationId=quotations.quotationId  ";
      $filt .= "WHERE NOT supplier_show IS NULL AND supplier_show!='' ";
      $filt .= "AND brandId IN (" . implode(",", StoneBrands::getBretiBrandIds()) . ") ";
      $filt .= "AND statusId = " . Status::STATUS_DELIVERED . " ";

      if ($this->dataTable->hasFormElementValue("search")) {
        $searchstr = DbHelper::escape($this->dataTable->getFormElementValue("search"));
        $filt .= "AND ( ";
        $filt .= "quotations_extra.addressDeliveryId LIKE '%" . $searchstr . "%' ";
        $filt .= ") ";
      }

      /** TOTALS */
      $total_count = Quotations::count_all_by([]);
      $total_count_filtered = Quotations::count_all_by([], $filt);

      /** GET DATA */
      $query = "SELECT * FROM " . Quotations::getTablename() . " ";
      $query .= $filt;
      $query .= $this->dataTable->getSortQuery();
      $query .= $this->dataTable->getPager()->getLimitQuery();

      $result = DBConn::db_link()->query($query);

      $table_data = [];
      while ($row = $result->fetch_array()) {
        $column_count = 0;

        $quotation = (new Quotations())->hydrateNext($row, $column_count);
        $quotation_extra = (new QuotationsExtra())->hydrateNext($row, $column_count);
        $brands = AppModel::mapObjectIds(StoneBrands::find_all_by(["brandId" => StoneBrands::getBretiBrandIds()], "ORDER BY displayOrder"), "brandId");
        $stone = Stones::find_by(['stoneId' => $quotation->stoneId]);

        if ($stone) {
          $size = StoneSizes::find_by(["sizeId" => $stone->sizeId]);
        }

        $supplier_time = strtotime("-1 WEEKS", intval($quotation->getDueDate("U")));

        $actions_html = "";
        $actions_html .= " " . BtnHelper::getPrintPDF('?action=downloadbretipdf&id=' . $quotation->quotationId, __('Bekijk Breti pdf'), '_blank');

        $table_data[] = [
          'DT_RowId'          => $quotation->quotationId,
          'addressDeliveryId' => $quotation_extra->addressDeliveryId,
          'supplier_time'     => date("Y-W", $supplier_time),
          'productionDate'    => $quotation->getProductionDate(),
          'quotationNumber'   => $quotation->getQuotationNumberFull(),
          'brand_name'        => $brands[$quotation->brandId]->name,
          'stone_name'        => $size->name ?? '',
          'meters'            => $quotation->meters,
          'projectValue'      => $quotation->projectValue,
          'surface'           => $quotation->getSurface($stone) ?? '',
          'actions'           => $actions_html,
          'produceDate'       => $quotation->getProduceDate(),
        ];
      }


      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => $total_count,
        'recordsFiltered' => $total_count_filtered,
        'draw'            => (int)$_POST['draw'],
      ]);
    }

    public function executeCreate_breti_invoice() {
      $quotation_ids = explode(',', $_GET['quotation_ids']);

      $quotations = [];
      foreach ($quotation_ids as $quotation_id) {
        $quotation = Quotations::find_by(['quotationId' => $quotation_id]);
        $quotations[] = $quotation;
        $quotation_extra = QuotationsExtra::find_by(['quotationId' => $quotation->quotationId]);
        $brands = AppModel::mapObjectIds(StoneBrands::find_all_by(["brandId" => StoneBrands::getBretiBrandIds()], "ORDER BY displayOrder"), "brandId");
        $stone = Stones::find_by(['stoneId' => $quotation->stoneId]);

        if ($stone) {
          $size = StoneSizes::find_by(["sizeId" => $stone->sizeId]);
        }

        $quotation->quotation_extra = $quotation_extra;
        $quotation->brands = $brands;
        $quotation->size = $size;
      }

      $this->quotations = $quotations;
      $this->quotation_ids = $_GET['quotation_ids'];
      $this->breti_invoice_number = 3;

      if (isset($_POST['create_invoice'])) {
        $breti_invoice = new BretiInvoice();

        $last_invoice_number = intval(BretiInvoice::getLastInvoiceNumber());
        $last_invoice_id = intval(BretiInvoice::getLastInvoiceId());
        $breti_invoice->breti_invoice_number = $last_invoice_number += 1;
        $breti_invoice->breti_invoice_date = date("Y-m-d");
        $breti_invoice->insertTS = date("Y-m-d H:i:s");
        $total_price = 0;

        foreach ($quotations as $quotation) {
          $breti_quotation = new BretiQuotation();
          $breti_quotation->quotation_id = $quotation->quotationId;
          $breti_quotation->breti_invoice_id = $last_invoice_id;
          $breti_quotation->save();

          $quotation->statusId = Status::STATUS_DELIVERED;

          $total_price += $quotation->projectValue;
        }

        $breti_invoice->total_project_value = $total_price;
        $breti_invoice->save();

        $this->executeDownloadpdfmultiple($breti_invoice->breti_invoice_number, true);

        ResponseHelper::redirectMessage('De PDF is succesvol gedownload, u kunt hem nu bekijken op het Geleverd scherm', PageMap::getUrl("M_SUPPLIER_ORDERS_EXT_INVOICES"));
      }
    }

    public function executeDownloadpdfmultiple($breti_invoice_number = '', $download = false) {
      $quotation_ids_arr = explode(',', $_GET['quotation_ids']);
      if (!$breti_invoice_number) {
        $breti_invoice_number = $_GET['nr'];
      }

      $isAdmin = $_SESSION['userObject']->usergroup == User::USERGROUP_SUPERADMIN || $_SESSION['userObject']->usergroup == User::USERGROUP_ADMIN;
      $pdf = new ProductionReceiptMultiplePdf($quotation_ids_arr, $breti_invoice_number, $download);
      $pdf->setIsAdmin($isAdmin);
      $pdf->generate();
    }

    public function executeDeleteinvoice() {
      $invoice_id = $_GET['id'];
      $breti_invoice = BretiInvoice::find_by_id($invoice_id);
      $breti_quotations = BretiQuotation::find_all_by(['breti_invoice_id' => $invoice_id]);

      foreach ($breti_quotations as $quotation) {
        $quotation->destroy();
      }
      $breti_invoice->destroy();

      $_SESSION['flash_message'] = "De factuur is succesvol terug gezet naar de Factureren pagina";
      ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
    }

    public function executePrices() {
      if (isset($_POST['reset'])) {
        unset($_SESSION['bsp_search'], $_SESSION['bsp_brand'], $_SESSION['bsp_type'], $_SESSION['bsp_endstone'], $_SESSION['bsp_display'], $_SESSION['bsp_colors'], $_SESSION['bsp_years']);
        ResponseHelper::redirect(reconstructQuery());
      }

      if (isset($_POST["add"])) {
        $add = StonePrices::find_by(["stoneId" => $_POST["missingsize"], "validFrom" => date("Y-01-01")]);
        if (!$add) {
          $add = new StonePrices();
          $add->stoneId = $_POST["missingsize"];
          $add->validFrom = date("Y-01-01");
          $add->validTo = "9999-12-31";
          $add->price = 999.99;
          $add->save();
        }
        MessageFlashCoordinator::addMessage("Steen prijs toegevoegd.");
        //redirect(reconstructQueryAdd()."action=mitrefactoredit&yearfrom=".date("Y")."&edit=Bewerk+factorlijst");
        ResponseHelper::redirect(reconstructQuery());
      }

      if (!isset($_SESSION['bsp_search'])) $_SESSION['bsp_search'] = '';
      if (!isset($_SESSION['bsp_brand'])) $_SESSION['bsp_brand'] = '';
      if (!isset($_SESSION['bsp_type'])) $_SESSION['bsp_type'] = '';
      if (!isset($_SESSION['bsp_endstone'])) $_SESSION['bsp_endstone'] = '';
      if (!isset($_SESSION['bsp_display'])) $_SESSION['bsp_display'] = 'true';
      if (!isset($_SESSION['bsp_colors'])) $_SESSION['bsp_colors'] = [];

      if (isset($_POST['go'])) {
        if ($_SESSION['bsp_brand'] != $_POST['bsp_brand']) {
          //brand is reset, reset colors
          $_POST['bsp_colors'] = [];
        }
        $_SESSION['bsp_search'] = trim($_POST['bsp_search']);
        $_SESSION['bsp_brand'] = trim($_POST['bsp_brand']);
        $_SESSION['bsp_type'] = trim($_POST['bsp_type']);
        $_SESSION['bsp_endstone'] = trim($_POST['bsp_endstone']);
        $_SESSION['bsp_display'] = trim($_POST['bsp_display']);

        if (isset($_POST['bsp_colors'])) {
          $_SESSION['bsp_colors'] = $_POST['bsp_colors'];
        }
        else {
          $_SESSION['bsp_colors'] = [];
        }
        ResponseHelper::redirect(reconstructQuery());
      }

      $stones = $this->getStonesBreti(isset($_POST['edit']) || isset($_POST['editbatch']));

      if ($_SESSION['bsp_brand'] != "") {

        $query = "SELECT * FROM " . Stones::getTablename() . " ";
        $query .= "LEFT JOIN " . StonePrices::getTablename() . " ON stone_prices.stoneId=stones.stoneId ";
        $query .= " WHERE stone_prices.id IS NULL ";
        $query .= " AND brandId='" . $_SESSION['bsp_brand'] . "' ";
        $query .= " GROUP BY stones.stoneId ";
        $query .= " ORDER BY name ";
        $sizes_now = [];
        $result = DBConn::db_link()->query($query);
        while ($row = $result->fetch_row()) {
          $size = new Stones();
          $size->hydrate($row);
          $size->from_db = true;
          $sizes_now[] = $size;
        }
        $this->missing_sizes = $sizes_now;
      }

      $colors = [];
      if ($_SESSION['bsp_brand'] != "") {
        $colors = AppModel::mapObjectIds(StoneColors::getColors($_SESSION['bsp_brand']), 'colorId');
      }
      $this->colors = $colors;
      $this->brands = AppModel::mapObjectIds(StoneBrands::find_all("WHERE brandId IN (" . implode(",", StoneBrands::getBretiBrandIds()) . ") AND NOT brandId IN (" . implode(",", StoneBrands::getBrandidsWithVensterbanken()) . ") ORDER BY displayOrder"), "brandId");
      if ($_SESSION['bsp_brand'] != "" && !isset($this->brands[$_SESSION['bsp_brand']])) {
        unset($_SESSION['bsp_brand']);
        ResponseHelper::redirect(reconstructQueryAdd());
      }
      $this->items = $stones;
      $this->prices_next_year = StonePrices::getPricesNextYear();

    }

    private function getStonesBreti($edit = false) {

      $this->pager = new Pager();
      $this->pager->setWriteCount(true);
      $this->pager->handle();

      $filt = ' ';
      if ($_SESSION['bsp_brand'] != "") {
        if ($_SESSION['bsp_brand'] != "") {
          $filt .= " AND brandId='" . $_SESSION['bsp_brand'] . "' ";
        }
        $filt .= " AND type!='" . Stones::TYPE_VENSTERBANK . "' ";
        if ($_SESSION['bsp_type'] != "") {
          $filt .= " AND type='" . $_SESSION['bsp_type'] . "' ";
        }
        if ($_SESSION['bsp_endstone'] != "") {
          $filt .= " AND endstone='" . $_SESSION['bsp_endstone'] . "' ";
        }
        if ($_SESSION['bsp_display'] != "") {
          $filt .= " AND display='" . $_SESSION['bsp_display'] . "' ";
        }
        if (count($_SESSION['bsp_colors']) > 0) {
          $filt .= " AND colorId IN (" . implode(",", $_SESSION['bsp_colors']) . ") ";
        }
//        if ($_SESSION['bsp_year'] == "now") {
//          $filt .= " AND validFrom<='" . date("Y-m-d") . "' AND validTo>'" . date("Y-m-d") . "' ";
//        }
//        elseif ($_SESSION['bsp_year'] != "") {
//          $filt .= " AND YEAR(validFrom)='" . $_SESSION['bsp_year'] . "' ";
//        }
        if ($_SESSION['bsp_search'] != "") {
          $searchval = escapeForDB($_SESSION['bsp_search']);
          $filt .= " AND (";
          $filt .= " name LIKE '%" . $searchval . "%' ";
          $filt .= ")";
        }

      }

      $query = "SELECT * FROM " . Stones::getTablename() . " ";
      $query .= "JOIN " . StonePrices::getTablename() . " ON stone_prices.stoneId=stones.stoneId ";
      $query .= "WHERE 1 ";
      $query .= " AND validFrom<='" . date("Y-m-d") . "' AND validTo>'" . date("Y-m-d") . "' ";
      $query .= "AND brandId IN (" . implode(",", StoneBrands::getBretiBrandIds()) . ") ";
      $query .= $filt;

      if (!$edit) {
        $result = DBConn::db_link()->query($query);
        $this->pager->count = $result->num_rows;
        if (!$this->pager->count) $this->pager->count = 0;
      }
      $query .= "ORDER BY name";
      if (!$edit) {
        $query .= $this->pager->getLimitQuery();
      }

      $result = DBConn::db_link()->query($query);
      $stones = [];
      while ($row = $result->fetch_row()) {
        $stone = new Stones();
        $stone->hydrate($row);
        $stone->from_db = true;
        $stone->price = new StonePrices();
        $stone->price->hydrate($row, count(Stones::columns));
        $stone->price->from_db = true;
        $stones[] = $stone;
      }
      return $stones;
    }

  }