<script setup>
import { ref, watch } from 'vue'
import {useMarkerStore} from "../store/marker";
import axios from 'axios'
import {customIcons} from "../helpers/IconHelper";
import {useFilterStore} from "../store/filter";

const orders = ref([])

const markerStore = useMarkerStore()
watch(() => markerStore.selectedMarker, (marker) => {
  getOrdersInLocation(marker)
})

const filterStore = useFilterStore()
async function getOrdersInLocation(location) {
  if (!location) return orders.value = []

  const params = {
    country: location.country,
    zipcode: location.zipcode,
    street: location.street,
  }

  if (filterStore.showContainers) {
    params.showContainers = 1
  }
  const { data } = await axios.get('?action=getorders', { params })
  orders.value = data
}

const getIconSrcFromIcon = (icon) => {
  const iconFile = customIcons[icon].icon
  return `/src/assets/${iconFile}`
}
</script>

<template>
  <v-card v-if="orders.length">
    <v-card-title class="text-h3">
      {{ orders[0].deliverDomestic }}
    </v-card-title>

    <v-expansion-panels>
      <v-expansion-panel v-for="order in orders" :key="order.orderId">
        <v-expansion-panel-title>
          <div class="d-flex align-center">
            <v-img
              :src="getIconSrcFromIcon(order.icon)"
              class="mr-2"
              width="16"
              height="16"
            ></v-img>

            {{ order.orderNumber }}
            {{ order.companyName }}
            <strong v-if="order.plannedDeliveryDate" class="text-red ml-2">P</strong>
          </div>
        </v-expansion-panel-title>

        <v-expansion-panel-text>
          <div class="order-details">
            <p v-if="order.plannedDeliveryDate">
              <strong>INGEPLANNED: {{ order.plannedDeliveryDate }}</strong>
            </p>
            <p v-if="order.orderDate">
              Besteldatum: {{ order.orderDate }}
            </p>
            <p v-if="order.promisedDate">
              Leverdatum: {{ order.promisedDate }} (week {{ order.promisedWeek }})
            </p>
            <p v-if="order.orderNotes" class="text-red my-1" style="white-space: pre-line;">
              {{ order.orderNotes }}
            </p>
            <p v-if="order.meters">
              Meters: {{ order.meters }}
            </p>
            <p v-if="order.containerNumber">
              Bakken: bak {{ order.containerNumber }}
            </p>
            <p>
              <a :href="order.orderLink" target="_blank">
                Bekijk {{ order.orderNumber }}
              </a>
            </p>
            <p>
              <a :href="order.routeLink" target="_blank">
                Plan voor route
              </a>
            </p>
          </div>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>
  </v-card>
</template>

<style>
.v-expansion-panel--active {
  margin-top: 0 !important;
}
</style>