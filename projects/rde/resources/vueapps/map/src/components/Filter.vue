<template>
  <div id="map-filter">
    <v-card>
      <v-card-text>
        <template v-if="statusStore.statuses">
          <v-row>
            <v-col
                v-for="truck in truckStore.trucks"
                :key="truck.id"
                cols="6"
                class="pa-1"
            >
              <v-btn
                  size="small"
                  variant="outlined"
                  block
                  @click="truckStore.truckClicked(truck)"
              >
                <div class="text-center">
                  <div>{{ truck.name }}</div>
                  <div class="text-caption">{{ truck.licence }}</div>
                </div>
              </v-btn>
            </v-col>
            <v-col cols="12" class="pa-0">
              <v-text-field
                  v-model="filterStore.dateRoute4weeks"
                  type="date"
                  bg-color="white"
                  density="compact"
                  hide-details
              />
            </v-col>
            <v-col cols="12" class="pa-0">
              <v-checkbox
                  v-model="statusStore.allStatusesSelected"
                  label="Alle statussen"
                  color="primary"
                  hide-details
                  @change="onSelectAllChange"
              />
            </v-col>
            <v-col
              v-for="(name, status) in statusStore.statuses"
              :key="`status-${status}`"
              cols="4"
              class="pa-0"
            >
              <v-checkbox
                v-model="statusStore.selectedStatuses"
                :value="status"
                :color="getStatusColor(status)"
                hide-details
              >
                <template v-slot:label>
                  <v-img
                    :src="getIconSrcFromStatus(status)"
                    :title="name"
                    width="15"
                  />
                </template>
              </v-checkbox>
            </v-col>

            <v-col cols="12" class="pa-0">
              <v-checkbox
                  v-model="filterStore.showContainers"
                  label="Bakken & Rekken"
                  color="primary"
                  hide-details
              />
            </v-col>
            <v-col cols="12" class="pa-0">
              <v-checkbox
                  v-model="filterStore.showGpsBuddy"
                  label="GPS Buddy"
                  color="primary"
                  hide-details
              />
            </v-col>
            <v-col cols="12" class="pa-0">
              <v-checkbox
                  v-model="filterStore.showPoi"
                  label="Nuttige punten"
                  color="primary"
                  hide-details
              />
            </v-col>
            <v-col cols="12" class="pa-0">
              <v-checkbox
                  v-model="filterStore.smallOrders"
                  label="Kleine Orders"
                  color="primary"
                  hide-details
              />
            </v-col>
            <v-col cols="12" class="pa-0">
              <v-checkbox
                  v-model="filterStore.showBrandChinese"
                  label="Chinees Natuursteen"
                  color="primary"
                  hide-details
              />
            </v-col>
            <v-col cols="12" class="pa-0 mb-3">
              <v-checkbox
                  v-model="filterStore.showBrandBelgium"
                  label="Belgisch Arduin / Zimbabwe"
                  color="primary"
                  hide-details
              />
            </v-col>
          </v-row>
        </template>
        <v-progress-circular
          v-else
          indeterminate
          color="primary"
        />
      </v-card-text>
    </v-card>
    <selected-location class="mt-2"/>
  </div>
</template>

<script setup>
import {onMounted} from 'vue'
import { getStatusColor } from '../helpers/ColorHelper'
import {customIcons, statusIcons} from "../helpers/IconHelper";
import {useStatusStore} from "../store/status";
import {useTruckStore} from "../store/truck";
import {useFilterStore} from "../store/filter";
import SelectedLocation from "./SelectedLocation.vue";

const statusStore = useStatusStore()
const truckStore = useTruckStore()
const filterStore = useFilterStore()

const onSelectAllChange = (event) => {
  const checked = event.target.checked
  statusStore.toggleAllStatuses(checked)
}
const getIconSrcFromStatus = (status) => {
  const iconName = statusIcons[status]
  const iconFile = customIcons[iconName].icon
  return `/src/assets/${iconFile}`
}

onMounted(async () => {
  await statusStore.getStatuses()
  await truckStore.getTrucks()
})
</script>

<style lang="scss">
#map-filter {
  border-right: 1px solid grey;
  height: 100%;
  overflow: auto;
  padding: 15px;

  .v-col {
    height: 5em;
  }
}
</style>
