<template>
  <div id="step1" class="wizard" v-cloak v-if="!loading">

    <div class="alert alert-danger" v-if="errors.length>0">
      <PERSON>r <PERSON><PERSON><PERSON> f<PERSON><PERSON><PERSON> opget<PERSON>en, controleer uw invoer:
      <ul>
        <li v-for="error in errors" :key="error">{{error}}</li>
      </ul>
    </div>

    <form method="post" :ref="setForm">

      <div class="form-row">
        <label class="col3 col-form-label">Project naam</label>
        <div class="col1">
          <div v-if="is_valid.projectName" class="input-validation-icon">
            <img src="/projects/rde/templates/frontend/images/check_green.svg" alt="" width="20" />
          </div>
        </div>
        <div class="col7">
          <input type="text" v-model="quotation.projectName" name="projectName" placeholder="Project naam"  class="form-input" autofocus :class="{ 'has-error': !is_valid }" maxlength="30"/>
          <p class="input-error-message" v-if="input_errors.projectName"> {{ input_errors.projectName }}</p>
        </div>
        <div class="col1">
          <GsdPopper content="Naam van project of offerte">
            <a class="question-mark-inline fa fa-info-circle"></a>
          </GsdPopper>
        </div>
      </div>

      <div class="form-row" :class="{ disabled: !is_active_projectReference }">
        <label class="col3 col-form-label">Project referentie</label>
        <div class="col1">
          <div v-if="is_active_projectReference" class="input-validation-icon">
            <img src="/projects/rde/templates/frontend/images/check_green.svg" alt="" width="20" />
          </div>
        </div>
        <div class="col7">
          <input type="text" v-model="quotation.projectReference" name="projectReference" :readonly="!is_active_projectReference"  placeholder="Project referentie" class="form-input" :class="{ 'has-error': !is_valid }"  maxlength="20" />
        </div>
        <div class="col1">
          <GsdPopper content="Uw referentie">
            <a class="question-mark-inline fa fa-info-circle"></a>
          </GsdPopper>
        </div>
      </div>

      <div class="form-row" :class="{ disabled: !is_active_brandId }">
        <label class="col3 col-form-label">{{ brandTextTitle }}</label>
        <div class="col1">
          <div v-if="is_valid.brandId && is_active_brandId" class="input-validation-icon">
            <img src="/projects/rde/templates/frontend/images/check_green.svg" alt="" width="20" />
          </div>
        </div>
        <div class="col7">
          <gsd-select
              ref="brandId"
              name="brandId"
              v-bind:no_selection_text="brandTextEmptyoption"
              v-bind:options="brands"
              v-bind:value="quotation.brandId"
              v-bind:disabled="!is_active_brandId"
              @changeValue="setBrandId"
          ></gsd-select>
        </div>
        <div class="col1">
        </div>
        <div v-if="materialLink!=''">
          <div class="col4"></div>
          <div class="col8" style="padding-top: 10px;">
            <a :href="materialLink" target="_blank" style="color: #333;">Meer informatie over de mogelijke materialen <span class="fa fa-chevron-right" style="color: #888888;"></span></a>
          </div>
        </div>
      </div>

      <div class="form-row" :class="{ disabled: !is_active_colorId }">
        <label class="col3 col-form-label">Kleur</label>
        <div class="col1">
          <div v-if="is_valid.colorId && is_active_colorId" class="input-validation-icon">
            <img src="/projects/rde/templates/frontend/images/check_green.svg" alt="" width="20" />
          </div>
        </div>
        <div class="col7">
          <color-select
              ref="colorSelect"
              v-show="!isIsosill"
              v-bind:quotation="quotation"
              v-bind:possible_colors="possible_colors"
              v-bind:color="color"
              v-bind:is_active_color_id="is_active_colorId"
              @changeColorId="setColorId"
          ></color-select>
          <ral-color-select
              v-show="isIsosill"
              :is-active="is_active_colorId"
              v-model="quotation"
              @ralColorChanged="setRalColor"
          />
        </div>
        <div class="col1">
          <GsdPopper :content="colorTextInfo">
            <a class="question-mark-inline fa fa-info-circle"></a>
          </GsdPopper>
        </div>
        <div class="col4"></div>
        <div class="col8" style="padding-top: 10px;">
          <a :href="colorLink" target="_blank" style="color: #333;">Meer informatie over de mogelijke kleuren <span class="fa fa-chevron-right" style="color: #888888;"></span></a>
        </div>
      </div>

      <div class="form-row" :class="{ disabled: !is_active_sizeId }" v-if="showModel && !showDiepteDikte">
        <label class="col3 col-form-label">Model</label>
        <div class="col1">
          <div v-if="is_valid.sizeId && is_active_sizeId" class="input-validation-icon">
            <img src="/projects/rde/templates/frontend/images/check_green.svg" alt="" width="20" />
          </div>
        </div>
        <div class="col7">
          <gsd-select-grouped
              name="sizeId"
              no_selection_text="Kies een model..."
              v-bind:options="possible_sizes"
              v-bind:value="quotation.sizeId"
              v-bind:disabled="!is_active_sizeId"
              @changeValue="setSizeId"
          ></gsd-select-grouped>
        </div>
        <div class="col1">
          <GsdPopper>
            <a class="question-mark-inline fa fa-info-circle"></a>
            <template #content>
              <div v-html="sizeTextInfo"></div>
            </template>
          </GsdPopper>
        </div>
      </div>

      <NaturalStoneWallCopingFlat
          name="NaturalStoneWallCopingFlatSizeId"
          v-bind:sizeId="quotation.sizeId"
          v-bind:sizes="sizes"
          v-bind:possible_sizes="possible_sizes"
          v-bind:disabled="!is_active_sizeId"
          @changeValue="setSizeId"
          v-if="showNaturalStoneWallCopingFlat"
      >
      </NaturalStoneWallCopingFlat>


      <div class="form-row" :class="{ disabled: !is_active_depth }" v-if="showDiepteDikte">
        <label class="col3 col-form-label">Diepte</label>
        <input name="sizeId" v-model="quotation.sizeId" type="hidden"/>
        <div class="col1">
          <div v-if="is_valid.depth && is_active_depth" class="input-validation-icon">
            <img src="/projects/rde/templates/frontend/images/check_green.svg" alt="" width="20" />
          </div>
        </div>
        <div class="col7">
          <div class="select">
            <select name="depth" class="depth" v-model="depth" :disabled="!is_active_depth" :class="{ 'has-error': errors.hasOwnProperty('depth') }">
              <option value="">
                Kies diepte...
              </option>
              <option v-for="ldepth in possible_depths" :value="ldepth" :key="ldepth">
                {{ ldepth }}
              </option>
            </select>
          </div>
        </div>
        <div class="col1">
          <GsdPopper content="Kies uw diepte.">
            <a class="question-mark-inline fa fa-info-circle"></a>
          </GsdPopper>
        </div>
      </div>

      <div class="form-row" :class="{ disabled: !is_active_thickness }" v-if="showDiepteDikte && !showCustomStone && !showCustomStoneNatuursteen">
        <label class="col3 col-form-label">Dikte</label>
        <div class="col1">
          <div v-if="is_valid.thickness && is_active_thickness" class="input-validation-icon">
            <img src="/projects/rde/templates/frontend/images/check_green.svg" alt="" width="20" />
          </div>
        </div>
        <div class="col7">
          <div>
            <div class="select">
              <select name="thickness" class="thickness" v-model="thickness" :disabled="!is_active_thickness" :class="{ 'has-error': errors.hasOwnProperty('thickness') }">
                <option value="">
                  Kies dikte...
                </option>
                <option v-for="thickness in possible_thickness" :value="thickness" :key="thickness">
                  {{ thickness }}
                </option>
              </select>
            </div>
          </div>
        </div>
        <div class="col1">
          <GsdPopper content="Kies uw dikte. Dikte voorkant / Dikte achterkant">
            <a class="question-mark-inline fa fa-info-circle"></a>
          </GsdPopper>
        </div>
      </div>

      <div class="form-row" :class="{ disabled: !is_active_custom_depth}" v-if="showDiepteDikte && showCustomStone">
        <label class="col3 col-form-label">Op maat gemaakt</label>
        <div class="col1">
          <div v-if="is_valid.custom_depth && is_active_custom_depth" class="input-validation-icon">
            <img src="/projects/rde/templates/frontend/images/check_green.svg" alt="" width="20" />
          </div>
        </div>
        <div class="col7 select_custom_wrapper">
          <div class="select_custom">
            <div>
              <label>A</label>
              <div class="select select_custom_depth">
                <select name="custom_depth" class="custom_depth" v-model="custom_depth" :disabled="!is_active_custom_depth" :class="{ 'has-error': errors.hasOwnProperty('custom_depth') }">
                  <option value="">
                    Kies diepte...
                  </option>
                  <option v-for="custom_depth in possible_custom_depths" :value="custom_depth" :key="custom_depth">
                    {{ custom_depth }}
                  </option>
                </select>
              </div>
            </div>
            <div>
              <label>B</label>
              <div class="select select_custom_thickness">
                <select name="custom_thickness" class="custom_thickness" v-model="custom_thickness" :disabled="!is_active_custom_thickness" :class="{ 'has-error': errors.hasOwnProperty('custom_thickness') }">
                  <option value="">
                    Kies dikte...
                  </option>
                  <option v-for="custom_thickness in possible_custom_thicknesses" :value="custom_thickness" :key="custom_thickness">
                    {{ custom_thickness }}
                  </option>
                </select>
              </div>
            </div>
            <div>
              <label>C</label>
              <div class="select select_custom_height">
                <select name="custom_height" class="custom_height" v-model="custom_height" :disabled="!is_active_custom_height" :class="{ 'has-error': errors.hasOwnProperty('custom_height') }">
                  <option value="">
                    Kies hoogte...
                  </option>
                  <option v-for="custom_height in possible_custom_heights" :value="custom_height" :key="custom_height">
                    {{ custom_height }}
                  </option>
                </select>
              </div>
            </div>
            <div>
              <label>D</label>
              <div class="select select_custom_width_click">
                <select name="custom_width_click" class="custom_width_click" v-model="custom_width_click" :disabled="!is_active_custom_width_click" :class="{ 'has-error': errors.hasOwnProperty('custom_width_click') }">
                  <option value="">
                    Breedte klik...
                  </option>
                  <option v-for="custom_width_click in possible_custom_width_clicks" :value="custom_width_click" :key="custom_width_click">
                    {{ custom_width_click }}
                  </option>
                </select>
              </div>
            </div>
            <div>
              <label>E</label>
              <div class="select select_custom_height_click">
                <select name="custom_height_click" class="custom_height_click" v-model="custom_height_click" :disabled="!is_active_custom_height_click" :class="{ 'has-error': errors.hasOwnProperty('custom_height_click') }">
                  <option value="">
                    Hoogte klik...
                  </option>
                  <option v-for="custom_height_click in possible_custom_height_clicks" :value="custom_height_click" :key="custom_height_click">
                    {{ custom_height_click }}
                  </option>
                </select>
              </div>
            </div>
          </div>
          <div class="custom_img">
            <img v-bind:src="customimage"/>
          </div>
        </div>
        <div class="col1">
          <GsdPopper content="Selecteer uw afmetingen. Op de afbeelding ziet uw welke letter bij welke afmeting behoort.">
            <a class="question-mark-inline fa fa-info-circle"></a>
          </GsdPopper>
        </div>
      </div>

      <div class="form-row" :class="{ disabled: !is_active_custom_depth}" v-if="showDiepteDikte && showCustomStoneNatuursteen">
        <label class="col3 col-form-label">Op maat gemaakt
          <div style="color: red;">{{customStoneNatuursteenError}}</div>
        </label>
        <div class="col1">
          <div v-if="is_valid.custom_depth && is_active_custom_depth" class="input-validation-icon">
            <img src="/projects/rde/templates/frontend/images/check_green.svg" alt="" width="20" />
          </div>
        </div>
        <div class="col7 select_custom_wrapper">
          <div class="select_custom">
            <div>
              <label>A</label>
              <input type="number" v-model="custom_depth" name="custom_depth" min="1" placeholder="Kies diepte"  class="form-input custom_input" autofocus maxlength="30" :readonly="!is_active_custom_depth" />
            </div>
            <div>
              <label>B</label>
              <input type="number" v-model="custom_thickness" name="custom_thickness" min="1"  placeholder="Kies dikte"  class="form-input custom_input" autofocus maxlength="30" :readonly="!is_active_custom_thickness" />
            </div>
            <div>
              <label>C</label>
              <input type="number" v-model="custom_height" name="custom_height" min="1"  placeholder="Kies hoogte"  class="form-input custom_input" autofocus maxlength="30" :readonly="!is_active_custom_height" />
            </div>
            <div>
              <label>D</label>
              <input type="number" v-model="custom_width_click" name="custom_width_click" min="0"  placeholder="Kies breedte"  class="form-input custom_input" autofocus maxlength="30" :readonly="!is_active_custom_width_click" />
            </div>
            <div>
              <label>E</label>
              <input type="number" v-model="custom_height_click" name="custom_height_click" min="0" placeholder="Hoogte klik"  class="form-input custom_input" autofocus maxlength="30" :readonly="!is_active_custom_height_click" />
            </div>
          </div>
          <div class="custom_img">
            <img v-bind:src="customimage"/>
          </div>
        </div>
        <div class="col1">
          <GsdPopper content="Selecteer uw afmetingen. Op de afbeelding ziet uw welke letter bij welke afmeting behoort.">
            <a class="question-mark-inline fa fa-info-circle"></a>
          </GsdPopper>
        </div>
      </div>

      <div class="form-row" :class="{ disabled: !is_active_endstone }" v-if="showEndstone">
        <label class="col3 col-form-label">{{ isIsosill ? "Type uiteinde" : "Eindsteen" }}</label>
        <div class="col1">
          <div v-if="is_valid.endstone && is_active_endstone" class="input-validation-icon">
            <img src="/projects/rde/templates/frontend/images/check_green.svg" alt="" width="20" />
          </div>
        </div>
        <div class="col7">

          <gsd-select
              ref="endstone"
              name="endstone"
              no_selection_text="Kies een elementeinde..."
              v-bind:options="possible_endstonetypes"
              v-bind:value="quotation.endstone"
              v-bind:disabled="!is_active_endstone"
              @changeValue="setEndstone"
          ></gsd-select>

        </div>
        <div class="col1">
          <GsdPopper
              :content="isIsosill
                ? 'Bij Isosill kiest u een type uiteinde: vlak, met opstaande zijkant of met stucprofiel. Dit heeft invloed op de aansluiting tegen het gevel- of pleisterwerk.'
                : 'Eindstenen zijn raamdorpelstenen met opstaande zijkantjes tegen de neggekant aan. Dit is ter voorkoming van okselvlekken op de gevel langs het kozijn. Wanneer u de optie \'Nee (geglazuurde zijkantjes)\' kiest, zijn de zijkantjes ter plaatse van de neggekanten geglazuurd.'">
            <a class="question-mark-inline fa fa-info-circle"></a>
          </GsdPopper>
        </div>
      </div>

      <div class="form-row"  v-if="all_valid" >
        <label class="col4 col-form-label">
          Uw selectie<br/>
          <a v-bind:href="stone.pdfLocation" v-if="stone.pdfLocation!=null && stone.pdfLocation!=''"  class="btn btn-secondary" target="_blank" style="margin-bottom: 15px;">Doorsnede PDF <span class="fa fa-download"></span></a>
        </label>
        <div class="col7 col12-xs">
          <img :src="stoneimage" class="stoneimage" />
        </div>
        <div class="col1">
        </div>
        <label class="col12 col-form-label" v-if="stone && stone.alert!=''" style="padding-top: 15px;">
          <span class="wizard_stonealert"><a class="fa fa-exclamation-circle" title="Let op"></a> {{stone.alert}}</span>
        </label>

      </div>


      <br/><br/>
      <input type="hidden" v-bind:value="stone.stoneId" name="stoneId" />
      <button @click.prevent="clickPrev()" type="button" name="prev" id="prev" class="btn" style="float: left;"><i class="fa fa-chevron-left"></i> Vorige stap</button>
      <button @click.prevent="clickNext()" type="button" name="next" id="next" class="btn" style="float: right;" v-bind:disabled="!all_valid">Doorgaan <i class="fa fa-chevron-right"></i></button>

    </form>

  </div>

</template>

<script>

import colorSelect from './ColorSelect';
import GsdPopper from './GsdPopper.vue';
import GsdSelectGrouped from "./GsdSelectGrouped";
import GsdSelect from "./GsdSelect";
import NaturalStoneWallCopingFlat from "./NaturalStoneWallCopingFlat.vue";
import ErrorHelper from "../helpers/ErrorHelper";
import RalColorSelect from "./RalColorSelect.vue";

export default {
  name: 'SelectStone',
  components: {
    RalColorSelect,
    GsdSelectGrouped,
    GsdSelect,
    GsdPopper,
    NaturalStoneWallCopingFlat,
    'color-select' : colorSelect,
  },

  data() {
    return {
      loading: true,
      is_valid: {},
      all_valid: false,
      is_active: {},
      errors: [],
      input_errors: {},
      form: null,

      brands: {},
      colors: {},
      color_ids: [],
      sizes: {},
      sizes_ids: [],
      possible_sizes: [],
      possible_colors: [],
      quotation: {},
      color: false,
      stone: false,
      customimage: '',
      showModel: true,
      showDiepteDikte: false,
      showCustomStone: false,
      showCustomStoneNatuursteen: false,
      showNaturalStoneWallCopingFlat: false,
      showEndstone : false,
      material: "",
      variant: "",
      isIsosill: false,

      depth: '',
      possible_depths: [],
      thickness: '',
      possible_thickness: [],

      custom_size_id: '',
      custom_depth: '',
      possible_custom_depths: [],
      custom_thickness: '',
      possible_custom_thicknesses: [],
      custom_height: '',
      possible_custom_heights: [],
      custom_width_click: '',
      possible_custom_width_clicks: [],
      custom_height_click: '',
      possible_custom_height_clicks: [],

      customStoneNatuursteenError: '',

      is_active_projectReference: false,
      is_active_brandId: false,
      is_active_colorId: false,
      is_active_sizeId: false,
      is_active_endstone: false,
      is_active_depth: false,
      is_active_thickness: false,
      is_active_custom_depth: false,
      is_active_custom_thickness: false,
      is_active_custom_height: false,
      is_active_custom_width_click: false,
      is_active_custom_height_click: false,
    }
  },

  created() {
    this.fetchData();
  },
  mounted() {
  },
  watch: {
    'quotation.projectName' : {
      handler(val, oldVal) {
        this.projectNameChanged(oldVal === undefined);
      },
      flush: 'post'
      //door dit te doen, gaat hij de triggers aanroepen in de volgorde van het aanpassen van de property
      //zonder deze vlag, is de volgorde van aanroepen random.
      //advies is eigenlijk gebruik zo min mogelijk watchers maar @change
    },
    'quotation.brandId' : {
      handler(val, oldVal) {
        this.brandIdChanged(oldVal === undefined);
      },
      flush: 'post'
    },
    'quotation.colorId' : {
      handler(val, oldVal) {
        this.colorIdChanged(oldVal === undefined);
      },
      flush: 'post'
    },
    'quotation.sizeId': {
      handler(val, oldVal) {
        this.sizeIdChanged(oldVal === undefined);
      },
      flush: 'post'
    },
    'quotation.endstone': {
      handler() {
        this.endstoneChanged();
      },
      flush: 'post'
    },
    depth : {
      handler() {
        this.depthChanged();
      },
      flush: 'post'
    },
    thickness : {
      handler() {
        this.thicknessChanged();
      },
      flush: 'post'
    },
    custom_depth : {
      handler() {
        this.customChanged();
      },
      flush: 'post'
    },
    custom_thickness : {
      handler(val, oldVal) {
        this.customChanged(oldVal === undefined);
      },
      flush: 'post'
    },
    custom_height : {
      handler() {
        this.customChanged();
      },
      flush: 'post'
    },
    custom_width_click : {
      handler() {
        this.customChanged();
      },
      flush: 'post'
    },
    custom_height_click : {
      handler() {
        this.customChanged();
      },
      flush: 'post'
    },
  },
  computed: {
    brandTextTitle() {
      return ["beton", "natuursteen"].includes(this.material)?"Materiaal":"Merk";
    },
    brandTextEmptyoption() {
      return ["beton", "natuursteen"].includes(this.material)?"Kies materiaal...":"Kies merk...";
    },
    colorTextInfo() {
      return this.material==="keramische"?"St. Joris en Wienerberger kennen vele verschillende kleuren. Voor speciale kleuren kunt u altijd contact opnemen.":"Selecteer uw kleur";
    },
    sizeTextInfo() {
      return this.material==="natuursteen"?'Kies uw model. Meer info <a href="/prefab-raamdorpels/neggemaattabel" target="_blank">hier</a>':"Er zijn maar liefst 11 verschillende modellen waardoor het moeilijk is om het juiste model te kiezen. Klik op het informatie bolletje om meer informatie over de verschillende maten te verkijgen.";
    },
    materialLink() {
      if(this.material==="natuursteen") {
        return '/natuursteen/soorten';
      }
      if(this.material==="beton") {
        return '';
      }
      return '/prefab-raamdorpels/modellen';
    },
    colorLink() {
      if(this.material==="natuursteen") {
        if(this.variant==="spekband") {
          return '/natuursteen/spekbanden';
        }
        if(this.variant==="muurafdekker") {
          return '/natuursteen/muurafdekkers';
        }
        if(this.variant==="vensterbank") {
          return '/natuursteen/vensterbank';
        }
        return '/natuursteen/raamdorpels';
      }
      if(this.material==="beton") {
        return '';
        //return '/beton';
      }
      return '/prefab-raamdorpels/kleuren';
    },
    possible_endstonetypes() {
      var filtered_array = [];

      if (this.isIsosill) {
        // Alleen deze types zijn toegestaan bij Isosill
        filtered_array.push({value: 'flat', name: 'Vlak'});
        filtered_array.push({value: 'standingside', name: 'Opstaande zijkant'});
        filtered_array.push({value: 'stuc', name: 'Stucprofiel'});
        return filtered_array;
      }

      var hasnormal = false;
      var hasgrooves = false;
      for (var i = 0; i < this.sizes.length; i++) {
        if (this.sizes[i].brandId == this.quotation.brandId && this.sizes[i].stone.colorId == this.quotation.colorId && this.sizes[i].stone.sizeId == this.quotation.sizeId) {
          if(this.sizes[i].stone.endstone!='false') {
            if(this.sizes[i].stone.endstone=='left' || this.sizes[i].stone.endstone=='right') {
              hasnormal = true;
            }
            else if(this.sizes[i].stone.endstone=='leftg' || this.sizes[i].stone.endstone=='rightg') {
              hasgrooves = true;
            }
          }
        }
      }
      if(hasnormal) {
        filtered_array.push({value: 'true', name: "Ja (opstaande zijkantjes)"});
      }
      if(hasgrooves) {
        filtered_array.push({value: 'true_grooves', name: "Ja (groeven)"});
      }

      if(this.color && !this.color.glaced) { //ijzerklinker
        filtered_array.push({value: 'false', name: "Nee (ongeglazuurde zijkantjes)"});
      }
      else if(this.stone!=null && this.stone.endstones_required=="0") {
        filtered_array.push({value: 'false', name: "Nee (geglazuurde zijkantjes)"});
      }

      // return [];
      return filtered_array;
    },
    stoneimage() {
      let lstoneimage_filename = "geen.jpg";
      if(this.stone.image!='' && this.stone.image!=null && (this.quotation.endstone==="false" || this.quotation.endstone===false)) {
        lstoneimage_filename = this.stone.image;
      }
      else {
        //kijk of er een foto is van de kantjes
        for (let i = 0; i < this.sizes.length; i++) {
          if (this.sizes[i].brandId == this.quotation.brandId && this.sizes[i].stone.colorId == this.quotation.colorId && this.sizes[i].stone.sizeId == this.quotation.sizeId) {
            if(this.sizes[i].stone.endstone!='false' && this.sizes[i].stone.image!="" && this.sizes[i].stone.image!=null) {
              if(this.quotation.endstone=="true_grooves" && (this.sizes[i].stone.endstone=="leftg" || this.sizes[i].stone.endstone=="rightg")) {
                lstoneimage_filename = this.sizes[i].stone.image;
                break;
              }
              else if((this.quotation.endstone==true || this.quotation.endstone=="true") && (this.sizes[i].stone.endstone=="left" || this.sizes[i].stone.endstone=="right")) {
                lstoneimage_filename = this.sizes[i].stone.image;
                break;
              }
            }
          }
        }
      }
      return "//www.raamdorpel.nl/images/thresholds/" + lstoneimage_filename;
    }

  },
  methods: {
    fetchData() {
      fetch('?action=wizard&step=1&json=1', {
        headers: {'Content-type': 'application/json'},
      })
          .then(res => res.json())
          .then((response) => {

            if (ErrorHelper.handleError(this, response)) return;

            this.quotation = response.data.quotation;
            this.colors = response.data.colors;
            this.sizes = response.data.sizes;
            this.showModel = response.data.showModel;
            this.showDiepteDikte = response.data.showDiepteDikte;
            this.showEndstone = response.data.showEndstone;
            this.showNaturalStoneWallCopingFlat = response.data.showNaturalStoneWallCopingFlat;
            this.material = response.data.material;
            this.variant = response.data.variant;
            this.isIsosill = response.data.isIsosill;

            this.brands = [];
            for (var key in response.data.brands) {
              let brand = response.data.brands[key];
              this.brands.push({
                name: brand.name,
                value: brand.brandId,
              })
            }

            if (this.showDiepteDikte && this.quotation.sizeId != "") {
              for (var i = 0; i < this.sizes.length; i++) {
                if (this.sizes[i].sizeId == this.quotation.sizeId) {
                  var name_parts = this.sizes[i].name.split(" x ");
                  this.depth = name_parts[0];
                  this.thickness = name_parts[1];
                  break;
                }
              }
            }
            if (this.quotation.custom_stone) {
              this.depth = "Op maat gemaakt";
              this.custom_depth = this.quotation.custom_stone.depth;
              this.custom_thickness = this.quotation.custom_stone.thickness;
              this.custom_height = this.quotation.custom_stone.height;
              this.custom_width_click = this.quotation.custom_stone.width_click;
              this.custom_height_click = this.quotation.custom_stone.height_click;
            }

            this.loading = false;

          })
          .catch((error) => {
            this.errors = [];
            this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op (" + error + ")");
          });
    },
    postData(direction) {

      const formData = new FormData(this.form); // reference to form element
      formData.append(direction, 1);

      fetch('?action=wizard&step=1&json=1', {
        method: "POST",
        headers: {
          // 'Content-type': 'application/json',
          "Accept": "application/json",   // expected data sent back
        },
        body: formData
      })
          .then(res => res.json())
          .then((response) => {

            if (ErrorHelper.handleError(this, response)) return;

            this.errors = response.data.errors;
            if ("redirect" in response) {
              location.href = response.redirect;
            }
          })
          .catch((error) => {
            this.errors = [];
            this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op (" + error + ")");
          });
    },
    clickPrev() {
      this.postData("prev");
    },
    clickNext() {
      this.postData("next");
    },
    getPossibleColors() {
      var filtered_array = {
        "Ongeglazuurd":[],
        "Geglazuurd - standaard":[],
        "Geglazuurd - speciaal":[],
      };
      this.color_ids = [];
      for (var i = 0; i < this.colors.length; i++) {
        if (this.colors[i].brandId == this.quotation.brandId) {
          var group;
          if(this.colors[i].glaced==="true") {
            if(this.colors[i].common==="true") {
              group = filtered_array["Geglazuurd - standaard"];
            }
            else {
              group = filtered_array["Geglazuurd - speciaal"];
            }
          }
          else {
            group = filtered_array["Ongeglazuurd"];
          }
          group.push(this.colors[i])
          this.color_ids.push(this.colors[i].colorId);
        }
      }
      if(filtered_array["Ongeglazuurd"].length ==0) delete filtered_array["Ongeglazuurd"];
      if(filtered_array["Geglazuurd - standaard"].length ==0) delete filtered_array["Geglazuurd - standaard"];
      if(filtered_array["Geglazuurd - speciaal"].length ==0) delete filtered_array["Geglazuurd - speciaal"];
      return filtered_array;
    },
    getPossibleSizesOptions() {

      var filtered_array = {
        "Standaard maten":[],
        "Speciale maten":[],
      };
      this.sizes_ids = [];
      for (var i = 0; i < this.sizes.length; i++) {
        if (this.sizes[i].brandId == this.quotation.brandId && this.sizes[i].stone.colorId == this.quotation.colorId) {
          var group;
          if (this.sizes[i].common==="true") {
            group = filtered_array["Standaard maten"];
          }
          else {
            group = filtered_array["Speciale maten"];
          }

          if (this.sizes_ids.indexOf(this.sizes[i].sizeId) === -1) {
            let size = this.sizes[i];
            this.sizes_ids.push(size.sizeId);
            let option = {
              name: size.name,
              value: size.sizeId,
              // if price can be shown in the wizard, uncomment this line
              // description: `€ ${size.price.toFixed(2).replace(".", ",")}/m`
            };
            group.push(option);
          }
        }
      }

      if(filtered_array["Standaard maten"].length ==0) delete filtered_array["Standaard maten"];
      if(filtered_array["Speciale maten"].length ==0) delete filtered_array["Speciale maten"];
      return filtered_array;
    },
    getPossibleDepths() {
      var depths = [];
      this.custom_size_id = '';
      for (var i = 0; i < this.sizes.length; i++) {
        if (this.sizes[i].brandId == this.quotation.brandId && this.sizes[i].stone.colorId == this.quotation.colorId) {
          if(this.sizes[i].name.indexOf("Op maat gemaakt") > -1) { //op maat gemaakt id se laatste keuze. deze moet gedefineerd zijn in de database.
            this.custom_size_id = this.sizes[i].sizeId;
            continue;
          }
          var name_parts = this.sizes[i].name.split(" x ");
          if(!depths.includes(name_parts[0])) {
            depths.push(name_parts[0]);
          }
        }
      }
      depths.sort();
      if(this.custom_size_id!='') {
        depths.unshift("Op maat gemaakt");
      }
      return depths;
    },
    getPossibleThickness() {
      var thickensses = [];
      for (var i = 0; i < this.sizes.length; i++) {
        if (this.sizes[i].brandId == this.quotation.brandId && this.sizes[i].stone.colorId == this.quotation.colorId) {
          var name_parts = this.sizes[i].name.split(" x ");
          if(this.depth==name_parts[0] && name_parts.length==2 && !thickensses.includes(name_parts[1])) {
            thickensses.push(name_parts[1]);
          }
        }
      }
      thickensses.sort();
      return thickensses;
    },
    getPossibleCustomDepth() { //A
      const values = [];
      for (let i = 80; i <= 350; i+=10) {
        values.push(i);
      }
      return values;
    },
    getPossibleCustomThickness() { //B
      var values = [];
      for (var i = 30; i <=120; i+=10) {
        values.push(i);
      }
      return values;
    },
    getPossibleCustomHeight() { //C
      var values = [];
      if(this.quotation.brandId==6 || this.quotation.brandId==7) { //belgisch arduin / zimbabwe
        var ct = parseInt(this.custom_thickness);
        for (var i = ct; i <=ct+70; i+=5) {
          if(i>=ct+10) {
            values.push(i);
          }
        }
      }
      else if(this.quotation.brandId==5) { //belgisch arduin budget, altijd tussen B+0 en B+30, stappen 5
        for (var t = this.custom_thickness; t <=this.custom_thickness+50; t+=5) {
          if(t>=this.custom_thickness) {
            values.push(t);
          }
        }
        // values.push(this.custom_thickness+20);
        // this.custom_height = this.custom_thickness+20;
      }
      else {
        for (var l = this.custom_thickness; l <=this.custom_thickness+50; l+=5) {
          values.push(l);
        }
      }
      return values;
    },
    getPossibleCustomWidthClicks() { //D
      const max_width = this.custom_depth - 40;

      const values = [];
      for (let i = 0; i <= max_width; i += 5) {
        values.push(i);
      }
      return values;
    },
    getPossibleCustomHeightClicks() { //E
      var values = [];
      if(this.quotation.brandId==5) { //belgisch arduin budget, E = C - B, deze loopt namelijk nooit af
        this.custom_height_click = parseInt(this.custom_height) - parseInt(this.custom_thickness);
        values.push(this.custom_height_click);
      }
      else {
        var max = parseInt(this.custom_height) - parseInt(this.custom_thickness) - 10
        for (var i = 0; i <= max; i += 5) {
          values.push(i);
        }
      }
      return values;
    },
    projectNameChanged(init) {
      this.is_valid['projectName'] = false;
      if(!this.quotation.projectName || this.quotation.projectName=="") {
        this.is_active_projectReference = false;
        this.is_active_brandId = false;
        this.is_active_colorId = false;
        this.quotation.colorId = '';
        this.quotation.sizeId = '';
        this.is_active_sizeId = false;
        this.quotation.depth = '';
        this.is_active_depth = false;
        this.quotation.thickness = '';
        this.is_active_thickness = false;
        this.quotation.endstone = '';
        this.is_active_endstone = false;
        if(!init) {
          this.input_errors['projectName'] = "Project naam is verplicht";
        }
      }
      else {
        this.is_valid['projectName'] = true;
        this.is_active_projectReference = true;
        this.is_active_brandId = true;
        delete this.input_errors['projectName'];
      }
      this.allValid();
    },
    brandIdChanged(init) {

      if(!this.is_active_brandId) return;
      this.is_valid['brandId'] = false;
      var clearColorid = true;

      if(!this.quotation.brandId || this.quotation.brandId==="") {
        this.is_active_colorId = false;
        this.quotation.colorId = '';
        this.quotation.sizeId = '';
        this.is_active_sizeId = false;
        this.quotation.depth = '';
        this.is_active_depth = false;
        this.quotation.thickness = '';
        this.is_active_thickness = false;
        this.quotation.endstone = '';
        this.is_active_endstone = false;
        this.input_errors['brandId'] = "Merk is verplicht";
      }
      else {
        this.is_valid['brandId'] = true;
        this.is_active_colorId = true;
        this.is_valid['colorId'] = false;
        this.is_valid['sizeId'] = false;
        this.is_valid['sizeId'] = false;
        this.is_valid['endstone'] = false;
        this.is_valid['custom_depth'] = false;

        this.possible_colors = this.getPossibleColors();

        if(this.color_ids.length==1) {
          //maar 1 mogelijkheid, selecteer deze
          this.quotation.colorId = this.color_ids[0];
          for (var i = 0; i < this.colors.length; i++) {
            if (this.colors[i].colorId == this.quotation.colorId) {
              this.color = this.colors[i];
              break;
            }
          }
          this.is_active_colorId = true;
          clearColorid = false;
        }
        else {
          //er zijn meerdere mogelijkheden. Is de huidig geselecteerde wel mogelijk?
          var found = false;
          for (i = 0; i < this.colors.length; i++) {
            if (this.colors[i].colorId == this.quotation.colorId) {
              this.color = this.colors[i];
              found = true;
              break;
            }
          }
          if(!found) {
            this.quotation.colorId = ''
            this.color = false;
          }
        }

      }

      if(!init) {
        if(clearColorid) this.quotation.colorId = '';
        this.quotation.sizeId = '';
        this.is_active_sizeId = false;
        this.quotation.endstone = '';
        this.is_active_endstone = false;

        this.depth = '';
        this.custom_depth = '';
        this.custom_thickness = '';
        this.custom_height = '';
        this.is_active_depth = false;
        this.is_active_custom_depth = false;
        this.is_active_custom_thickness = false;
        this.is_active_custom_height = false;

      }
      this.allValid();
    },
    setColorId(colorId) {
      this.quotation.colorId = colorId;
    },
    setRalColor(color) {
      this.quotation.ralColor = color;
    },
    setSizeId(sizeId) {
      this.quotation.sizeId = sizeId;
    },
    setBrandId(brandId) {
      this.quotation.brandId = brandId;
    },
    setEndstone(endstone) {
      this.quotation.endstone = endstone;
    },
    colorIdChanged(init) {
      if(!this.is_active_colorId) return;
      this.is_valid['colorId'] = false;
      var clearSizeid = true;
      if(!this.quotation.colorId || this.quotation.colorId=="") {
        this.is_active_sizeId = false;
        this.quotation.sizeId = '';
        this.quotation.depth = '';
        this.is_active_depth = false;
        this.quotation.thickness = '';
        this.is_active_thickness = false;
        this.quotation.endstone = '';
        this.depth = '';
        this.input_errors['colorId'] = "Merk is verplicht";
      }
      else {

        for (var i = 0; i < this.colors.length; i++) {
          if (this.colors[i].colorId == this.quotation.colorId) {
            this.color = this.colors[i];
            break;
          }
        }
        this.is_valid['colorId'] = true;
        this.is_active_sizeId = true;
        this.possible_sizes = this.getPossibleSizesOptions();
        console.log(this.possible_sizes);

        if(this.showDiepteDikte) {
          this.is_active_depth = true;
          this.possible_depths= this.getPossibleDepths();

          var found = false;
          for (var t = 0; t < this.possible_depths.length; t++) {
            var posdepth = this.possible_depths[t];
            if(posdepth==this.depth) {
              found = true;
              break;
            }
          }
          if(!found) {
            this.depth = '';
            this.is_valid['depth'] = false;
          }

        }

        if(!this.showModel && !this.showNaturalStoneWallCopingFlat) {
          this.quotation.endstone="false";
          this.endstoneChanged();
        }

        if(!init && this.sizes_ids.length==1) {
          //maar 1 mogelijkheid, selecteer deze
          this.quotation.sizeId = this.sizes_ids[0];
          this.is_active_sizeId = true;
          clearSizeid = false;
          this.sizeIdChanged(false);
        }
      }


      if(!init) {
        if(clearSizeid) this.quotation.sizeId = '';
        if(this.showModel || this.showNaturalStoneWallCopingFlat) {
          this.quotation.endstone = '';
        }
        this.is_active_endstone = false;

        this.depth = '';
        this.custom_depth = '';
        this.custom_thickness = '';
        this.custom_height = '';
        // this.is_active_depth = false;
        this.is_active_custom_depth = false;
        this.is_active_custom_thickness = false;
        this.is_active_custom_height = false;
        this.is_valid['custom_depth'] = false;
        this.is_valid['endstone'] = false;
      }

      this.allValid();
    },
    sizeIdChanged(init) {
      if(!this.is_active_sizeId) return;
      this.is_valid['sizeId'] = false;
      if(!this.quotation.sizeId || this.quotation.sizeId=="") {
        this.quotation.endstone = '';
        this.is_active_endstone = false;
        this.input_errors['sizeId'] = "Model is verplicht";
        this.stone = {};
      }
      else {
        for (var i = 0; i < this.sizes.length; i++) {
          if (this.sizes[i].brandId == this.quotation.brandId && this.sizes[i].stone.colorId == this.quotation.colorId && this.sizes[i].stone.sizeId == this.quotation.sizeId) {
            if(this.sizes[i].stone.endstone=='false') {
              this.stone = this.sizes[i].stone;
              break;
            }
          }
        }
        this.is_valid['sizeId'] = true;
        this.is_active_endstone = true;
        if(this.showDiepteDikte) {
          this.is_active_depth = true;
        }

        if(!this.showEndstone) {
          this.quotation.endstone="false";
          this.endstoneChanged();
        }

      }
      if(!init && this.showEndstone) {
        this.quotation.endstone = '';
      }
      this.allValid();

      this.mayOrder();

    },
    depthChanged() {
      if(!this.is_active_depth) return;
      if(!this.showDiepteDikte) return;
      // if(!this.showDiepteDikte && this.quotation.sizeId=="") return;

      this.is_valid['depth'] = false;

      if(this.depth=="") {
        this.input_errors['depth'] = "Diepte";
        this.thickness = '';
        this.is_active_thickness = false;
        this.is_valid['thickness'] = false;
        this.showCustomStone = false;
        this.showCustomStoneNatuursteen = false;
        this.custom_depth = '';
        this.custom_thickness = '';
        this.custom_height = '';
        this.is_valid['custom_depth'] = false;
      }
      else {

        if(this.depth === "Op maat gemaakt") {
          if(this.material==="beton" && this.variant==="raamdorpel") {
            this.quotation.sizeId = this.custom_size_id;
            this.showCustomStone = true;
            this.is_active_custom_depth = true;
            this.is_active_custom_thickness = true;

            this.possible_custom_depths = this.getPossibleCustomDepth();
            this.possible_custom_thicknesses = this.getPossibleCustomThickness();
          }
          else { //natuursteen
            this.quotation.sizeId = this.custom_size_id;
            this.showCustomStoneNatuursteen = true;
            this.is_active_custom_depth = true;
            this.is_active_custom_thickness = true;

            this.possible_custom_depths = this.getPossibleCustomDepth();
            this.possible_custom_thicknesses = this.getPossibleCustomThickness();

            if(this.quotation.brandId==5) {
              this.customimage = '/projects/rde/templates/frontend/images/opmaatgemaakt_budget.png';
            }
            else {
              this.customimage = '/projects/rde/templates/frontend/images/opmaatgemaakt_default.png';
            }

          }
        }
        else {
          this.showCustomStone = false;
          this.showCustomStoneNatuursteen = false;
          this.custom_depth = '';
          this.custom_thickness = '';
          this.custom_height = '';
          this.is_valid['custom_depth'] = false;
        }

        this.is_valid['depth'] = true;
        this.is_active_thickness = true;
        this.possible_thickness = this.getPossibleThickness();
        var found = false;
        for (var i = 0; i < this.possible_thickness.length; i++) {
          var thick = this.possible_thickness[i];
          if (thick == this.thickness) {
            found = true;
            this.thicknessChanged(); //stond uit, weer aangezet aangezien bij wissel van diepte de verkeerde stone werd doorgezet.
            break;
          }
        }
        if (!found) {
          this.thickness = '';
        }


      }
      this.allValid();
    },
    thicknessChanged(init) {
      if(!this.is_active_thickness) return;
      if(!this.showDiepteDikte) return;

      this.is_valid['thickness'] = false;
      if(this.thickness=="") {
        this.quotation.endstone = '';
        this.is_active_endstone = false;
        this.input_errors['depth'] = "Dikte";
      }
      else {
        this.is_valid['thickness'] = true;
        this.is_active_thickness = true;

        //set sizeId
        this.quotation.sizeId = "";
        for (var i = 0; i < this.sizes.length; i++) {
          if (this.sizes[i].brandId == this.quotation.brandId && this.sizes[i].stone.colorId == this.quotation.colorId) {
            var name_parts = this.sizes[i].name.split(" x ");
            if(name_parts[0]==this.depth && name_parts[1]==this.thickness) {
              this.quotation.sizeId = this.sizes[i].sizeId;
              break;
            }
          }
        }

        if(!this.showEndstone) {
          this.quotation.endstone="false";
          this.endstoneChanged();
        }
      }
      if(!init && this.showEndstone) {
        this.quotation.endstone = '';
      }
      this.allValid();
    },
    customChanged() {
      if(this.showCustomStoneNatuursteen) {
        this.customChangedNatuursteen();
      }
      else {
        this.customChangedOther();
      }
    },
    customChangedNatuursteen() {
      if(!this.is_active_thickness) return;
      this.is_valid['thickness'] = false;

      //valideer diepte / A
      var minvalue = this.possible_custom_depths[0];
      var maxvalue = this.possible_custom_depths[this.possible_custom_depths.length-1];
      if(this.custom_depth=="" || !(this.custom_depth>=minvalue && this.custom_depth<=maxvalue)) {
        this.customStoneNatuursteenError = "A tussen " + minvalue + " en " + maxvalue;
        this.is_active_custom_thickness = false;
        this.is_active_custom_height = false;
        this.is_active_custom_width_click = false;
        this.is_active_custom_height_click = false;
        this.allValid();
        return;
      }

      //valideer dikte / B
      this.is_active_custom_thickness = true;
      minvalue = this.possible_custom_thicknesses[0];
      maxvalue = this.possible_custom_thicknesses[this.possible_custom_thicknesses.length-1];
      if(this.custom_thickness=="" || !(this.custom_thickness>=minvalue && this.custom_thickness<=maxvalue)) {
        this.customStoneNatuursteenError = "B tussen " + minvalue + " en " + maxvalue;
        this.is_active_custom_height = false;
        this.is_active_custom_width_click = false;
        this.is_active_custom_height_click = false;
        this.allValid();
        return;
      }

      //valideer hoogte / C
      this.is_active_custom_height = true;
      this.possible_custom_heights = this.getPossibleCustomHeight();
      if(this.possible_custom_heights.length==1) {
        this.custom_height = this.possible_custom_heights[0];
      }
      minvalue = this.possible_custom_heights[0];
      maxvalue = this.possible_custom_heights[this.possible_custom_heights.length-1];
      if(this.custom_height=="" || !(parseInt(this.custom_height)>=minvalue && parseInt(this.custom_height)<=maxvalue)) {
        this.customStoneNatuursteenError = "C tussen " + minvalue + " en " + maxvalue;
        this.is_valid['custom_depth'] = false;
        this.is_active_custom_width_click = false;
        this.is_active_custom_height_click = false;
        this.allValid();
        return;
      }

      //valideer D
      this.possible_custom_width_clicks = this.getPossibleCustomWidthClicks();
      this.is_active_custom_width_click = true;
      if(this.possible_custom_width_clicks.length==1) {
        this.custom_width_click = this.possible_custom_width_clicks[0];
      }
      minvalue = this.possible_custom_width_clicks[0];
      maxvalue = this.possible_custom_width_clicks[this.possible_custom_width_clicks.length-1];
      if(this.custom_width_click=="" || !(this.custom_width_click>=minvalue && this.custom_width_click<=maxvalue)) {
        this.customStoneNatuursteenError = "D tussen " + minvalue + " en " + maxvalue;
        this.is_valid['custom_depth'] = false;
        this.is_active_custom_height_click = false;
        this.allValid();
        return;
      }

      //valideer E
      this.is_active_custom_height_click = true;
      this.possible_custom_height_clicks = this.getPossibleCustomHeightClicks();
      if(this.possible_custom_height_clicks.length==1) {
        this.custom_height_click = this.possible_custom_height_clicks[0];
      }
      minvalue = this.possible_custom_height_clicks[0];
      maxvalue = this.possible_custom_height_clicks[this.possible_custom_height_clicks.length-1];
      if(this.custom_height_click==="" || !(this.custom_height_click>=minvalue && this.custom_height_click<=maxvalue)) {
        this.customStoneNatuursteenError = "E tussen " + minvalue + " en " + maxvalue;
        this.is_valid['custom_depth'] = false;
        this.allValid();
        return;
      }

      this.customStoneNatuursteenError = "";
      this.is_valid['thickness'] = true;
      this.is_valid['custom_depth'] = true;
      this.is_active_thickness = true;

      this.allValid();
    },
    customChangedOther() {
      if(!this.is_active_thickness) return;
      this.is_valid['thickness'] = false;
      if(this.custom_depth=="" || this.custom_thickness=="") {
        this.is_active_custom_height = false;
        this.is_active_custom_width_click = false;
        this.is_active_custom_height_click = false;
      }
      else {
        this.is_active_custom_height = true;
        this.possible_custom_heights = this.getPossibleCustomHeight();

        var found = false;
        for (var i = 0; i < this.possible_custom_heights.length; i++) {
          var cheight = this.possible_custom_heights[i];
          if(cheight==this.custom_height) {
            found = true;
            break;
          }
        }
        if(!found) {
          this.custom_height = '';
          this.is_valid['custom_depth'] = false;
          this.is_active_custom_width_click = false;
          this.is_active_custom_height_click = false;
        }

        if(this.custom_height!="") {

          //waarde D en E actief
          this.is_active_custom_width_click = true;
          this.possible_custom_width_clicks = this.getPossibleCustomWidthClicks();
          this.is_active_custom_height_click = true;
          this.possible_custom_height_clicks = this.getPossibleCustomHeightClicks();

          if(this.possible_custom_width_clicks.length==1) {
            this.custom_width_click = this.possible_custom_width_clicks[0];
          }

          found = false;
          for (var t = 0; t < this.possible_custom_width_clicks.length; t++) {
            var val = this.possible_custom_width_clicks[t];
            if (val == this.custom_width_click) {
              found = true;
              break;
            }
          }
          if(!found) {
            this.custom_width_click = '';
            this.is_valid['custom_depth'] = false;
          }

          if(found) {

            if(this.possible_custom_height_clicks.length==1) {
              this.custom_height_click = this.possible_custom_height_clicks[0];
            }

            found = false;
            for (t = 0; t < this.possible_custom_height_clicks.length; t++) {
              val = this.possible_custom_height_clicks[t];
              if(val==this.custom_height_click) {
                found = true;
                break;
              }
            }
            if(!found) {
              this.custom_height_click = '';
              this.is_valid['custom_depth'] = false;
            }
          }

          if(this.custom_width_click!="" && this.custom_height_click!="" &&  this.custom_height_click!="0") {
            this.is_valid['thickness'] = true;
            this.is_valid['custom_depth'] = true;
            this.is_active_thickness = true;
          }
        }
      }
      this.allValid();
    },
    customNatuursteenChanged() {
      if(!this.is_active_thickness) return;
      this.is_valid['thickness'] = false;
      if(this.custom_depth=="" || this.custom_thickness=="") {
        this.is_active_custom_height = false;
        this.is_active_custom_width_click = false;
        this.is_active_custom_height_click = false;
      }
      else {
        this.is_active_custom_height = true;
        this.possible_custom_heights = this.getPossibleCustomHeight();

        var found = false;
        for (var i = 0; i < this.possible_custom_heights.length; i++) {
          var cheight = this.possible_custom_heights[i];
          if(cheight==this.custom_height) {
            found = true;
            break;
          }
        }
        if(!found) {
          this.custom_height = '';
          this.is_valid['custom_depth'] = false;
          this.is_active_custom_width_click = false;
          this.is_active_custom_height_click = false;
        }

        if(this.custom_height!="") {

          //waarde D en E actief
          this.is_active_custom_width_click = true;
          this.possible_custom_width_clicks = this.getPossibleCustomWidthClicks();
          this.is_active_custom_height_click = true;
          this.possible_custom_height_clicks = this.getPossibleCustomHeightClicks();

          if(this.possible_custom_width_clicks.length==1) {
            this.custom_width_click = this.possible_custom_width_clicks[0];
          }

          found = false;
          for (i = 0; i < this.possible_custom_width_clicks.length; i++) {
            var val = this.possible_custom_width_clicks[i];
            if (val == this.custom_width_click) {
              found = true;
              break;
            }
          }
          if(!found) {
            this.custom_width_click = '';
            this.is_valid['custom_depth'] = false;
          }

          if(found) {

            if(this.possible_custom_height_clicks.length==1) {
              this.custom_height_click = this.possible_custom_height_clicks[0];
            }

            found = false;
            for (i = 0; i < this.possible_custom_height_clicks.length; i++) {
              val = this.possible_custom_height_clicks[i];
              if(val==this.custom_height_click) {
                found = true;
                break;
              }
            }
            if(!found) {
              this.custom_height_click = '';
              this.is_valid['custom_depth'] = false;
            }
          }

          if(this.custom_width_click!="" && this.custom_height_click!="" &&  this.custom_height_click!="0") {
            this.is_valid['thickness'] = true;
            this.is_valid['custom_depth'] = true;
            this.is_active_thickness = true;
          }
        }
      }
      this.allValid();
    },
    endstoneChanged() {
      if(!this.is_active_endstone) return;
      this.is_valid['endstone'] = false;
      if(this.quotation.endstone==="") {
        this.input_errors['endstone'] = "Eindsteen is verplicht";
      }
      else {
        this.is_valid['endstone'] = true;
        this.is_active_endstone = true;
      }
      this.allValid();
      this.mayOrder();
    },
    allValid() {
      if(this.material==="natuursteen" && this.variant==="vensterbank" && (this.quotation.projectName=='' || this.quotation.sizeId=='')) {
        this.all_valid = false;
      }
      else if(this.quotation.projectName=='' || (!this.showCustomStone && !this.showCustomStoneNatuursteen && this.showEndstone && this.quotation.endstone==="")) {
        this.all_valid = false;
      }
      else if(this.showCustomStone && (this.custom_thickness=='' || this.custom_width_click=='' || this.custom_height_click==='')) {
        this.all_valid = false;
      }
      else if(this.showCustomStoneNatuursteen && !this.is_valid['custom_depth']) {
        this.all_valid = false;
      }
      else if(!this.showCustomStone && !this.showCustomStoneNatuursteen && this.showDiepteDikte && (this.depth=='' || this.thickness=='')) {
        //show diepte + dikte
        this.all_valid = false;
      }
      else {
        this.all_valid = true;
      }
    },
    setForm(el) {
      this.form = el;
    },
    mayOrder() {
      //console.log("BesteL: "+this.stone.may_order);
      if(this.all_valid && this.stone.may_order=="0") {
        let mes = "De huidige keuze is helaas niet meer beschikbaar. Neem contact op, we adviseren u graag een passend alternatief.";
        if(this.stone.may_not_order_message!="") {
          mes = this.stone.may_not_order_message;
        }
        this.$swal("", mes, "error");
        this.all_valid = false;
        return false;
      }
      return true;
    }
  }
}

</script>
